import requests
import json
import os
from datetime import datetime
from PIL import Image, ImageDraw, ImageFont
import qrcode


class SimpleShippingLabelGenerator:
    def __init__(self):
        self.headers = {
            "accept": "application/json, text/plain, */*",
            "accept-language": "zh-CN,zh;q=0.9",
            "content-type": "application/json;charset=UTF-8",
            "mallid": "634418218777560",
            "origin": "https://agentseller-us.temu.com",
            "priority": "u=1, i",
            "referer": "https://agentseller-us.temu.com/mmsos/waybill.html?logistics_create_time=1750003200000%2C1752681599999&page_number=2&page_size=200&sort_type=1&call_begin_time=1750003200000&call_end_time=1752681599999&active_waybill_tab=0",
            "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"",
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": "\"Windows\"",
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "same-origin",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36",
            "x-document-referer": "https://agentseller-us.temu.com/main/authentication?redirectUrl=https%3A%2F%2Fagentseller-us.temu.com%2Fmmsos%2Fwaybill.html",
            "x-phan-data": "0aeJx7xMxiYPiIOSza0NzUyMzU3MDU1NzMWAfGszQwNjBB8AwNDUxjAT7WC4M"
        }
        self.cookies = {
            "api_uid": "CiCFY2h2Gge8HQBGEdGIAg==",
            "_nano_fp": "XpmyX5d8X0X8nqTxX9_JX7Kd7W6yX~XlC1RvKXhc",
            "_bee": "Guoq8zmpMw54MRLiHdZETgLhuZwVQapH",
            "njrpl": "Guoq8zmpMw54MRLiHdZETgLhuZwVQapH",
            "dilx": "8Y5zRfV0WSOxkuYUB-wow",
            "hfsc": "L3yPeow36T3/2p7PeQ==",
            "timezone": "Asia%2FShanghai",
            "webp": "1",
            "mallid": "634418218777560",
            "region": "0",
            "seller_temp": "N_eyJ0IjoiUU9oQ2pyS2FRUW9MenJrbkFzODNYVXRpdWJFZ2dVekU4bHA4UUtpemRxSEV2K2R4VzczS1lWWlFOV2tGUEVWR1U5WDl6K3V1QW1CZTdoQVQvQnVnYkE9PSIsInYiOjEsInMiOjEwMDAxLCJ1IjoyMzUxNDQ1MzE4NDUyMX0="
        }
        
    def get_package_data(self):
        """获取包裹数据"""
        url = "https://agentseller-us.temu.com/mms/eagle/package/main_batch_query"
        data = {
            "logistics_create_time": [1750003200000, 1752681599999],
            "page_number": 1,
            "page_size": 200,
            "sort_type": 1,
            "call_begin_time": 1750003200,
            "call_end_time": 1752681599
        }
        
        try:
            response = requests.post(
                url, 
                headers=self.headers, 
                cookies=self.cookies, 
                data=json.dumps(data, separators=(',', ':'))
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                print(f"API请求失败，状态码: {response.status_code}")
                print(f"响应内容: {response.text}")
                return None
                
        except Exception as e:
            print(f"请求异常: {e}")
            return None
    
    def generate_usps_label_image(self, package_info):
        """生成USPS风格的运单标签图片"""
        # 创建图片 (4x6英寸，300 DPI)
        width, height = 1200, 1800
        img = Image.new('RGB', (width, height), 'white')
        draw = ImageDraw.Draw(img)
        
        try:
            # 尝试加载字体
            title_font = ImageFont.truetype("arial.ttf", 36)
            header_font = ImageFont.truetype("arial.ttf", 24)
            text_font = ImageFont.truetype("arial.ttf", 18)
            small_font = ImageFont.truetype("arial.ttf", 14)
        except:
            # 如果无法加载字体，使用默认字体
            title_font = ImageFont.load_default()
            header_font = ImageFont.load_default()
            text_font = ImageFont.load_default()
            small_font = ImageFont.load_default()
        
        # USPS标题区域
        draw.rectangle([0, 0, width, 120], fill='white', outline='black', width=2)
        
        # USPS Logo区域 (简化版)
        draw.rectangle([20, 20, 200, 100], fill='navy')
        draw.text((30, 40), "USPS", fill='white', font=title_font)
        
        # APIs标题
        draw.text((width-200, 30), "USPS APIs", fill='black', font=header_font)
        
        # 邮资已付区域
        draw.rectangle([width-300, 60, width-20, 100], fill='lightgray', outline='black')
        draw.text((width-280, 70), "U.S. POSTAGE PAID", fill='black', font=small_font)
        
        # Ground Advantage标题
        y_pos = 140
        draw.text((20, y_pos), "USPS GROUND ADVANTAGE™", fill='black', font=header_font)
        
        # 发件人信息
        y_pos += 60
        sender_info = package_info.get('sender', {})
        draw.text((20, y_pos), f"FROM:", fill='black', font=text_font)
        y_pos += 25
        draw.text((20, y_pos), sender_info.get('name', 'HELEN'), fill='black', font=text_font)
        y_pos += 25
        draw.text((20, y_pos), sender_info.get('address', '4515 LITTLE JOHN ST'), fill='black', font=text_font)
        y_pos += 25
        draw.text((20, y_pos), sender_info.get('city_state_zip', 'BALDWIN PARK CA 91706'), fill='black', font=text_font)
        
        # RDC信息
        draw.text((width-150, y_pos-50), "RDC 01", fill='black', font=header_font)
        
        # 收件人信息
        y_pos += 80
        draw.rectangle([0, y_pos-10, width, y_pos+150], fill='white', outline='black', width=2)
        
        recipient_info = package_info.get('recipient', {})
        draw.text((20, y_pos+10), recipient_info.get('name', 'NORMA SANCHEZ'), fill='black', font=header_font)
        y_pos += 40
        draw.text((20, y_pos), recipient_info.get('address', '549 RAMOS CT'), fill='black', font=text_font)
        y_pos += 25
        draw.text((20, y_pos), recipient_info.get('city_state_zip', 'MILPITAS CA 95035'), fill='black', font=text_font)
        
        # 二维码 (左下角)
        y_pos += 60
        qr = qrcode.QRCode(version=1, box_size=3, border=1)
        qr.add_data(package_info.get('tracking_number', '9300110622200479930771'))
        qr.make(fit=True)
        qr_img = qr.make_image(fill_color="black", back_color="white")
        qr_img = qr_img.resize((120, 120))
        img.paste(qr_img, (20, y_pos))
        
        # 追踪号码标题
        y_pos += 140
        draw.text((20, y_pos), "USPS TRACKING # USPS Ship", fill='black', font=text_font)
        
        # 追踪号码条形码 (简化版)
        y_pos += 30
        tracking_number = package_info.get('tracking_number', '9300110622200479930771')
        
        # 简化的条形码绘制
        barcode_width = width - 40
        bar_width = barcode_width // len(tracking_number)
        
        for i, char in enumerate(tracking_number):
            x = 20 + i * bar_width
            if int(char) % 2 == 0:  # 简化的条形码逻辑
                draw.rectangle([x, y_pos, x + bar_width//2, y_pos + 60], fill='black')
        
        # 追踪号码文本
        y_pos += 70
        draw.text((20, y_pos), tracking_number, fill='black', font=header_font)
        
        # 右下角二维码
        qr2 = qrcode.QRCode(version=1, box_size=2, border=1)
        qr2.add_data("USPS")
        qr2.make(fit=True)
        qr2_img = qr2.make_image(fill_color="black", back_color="white")
        qr2_img = qr2_img.resize((80, 80))
        img.paste(qr2_img, (width-100, height-100))
        
        return img
    
    def process_packages(self):
        """处理包裹数据并生成标签"""
        print("正在获取包裹数据...")
        package_data = self.get_package_data()
        
        if package_data:
            print("API响应成功!")
            print(json.dumps(package_data, indent=2, ensure_ascii=False))
        else:
            print("无法获取包裹数据，使用示例数据生成标签")
        
        # 创建输出目录
        output_dir = "shipping_labels"
        os.makedirs(output_dir, exist_ok=True)
        
        # 示例包裹信息 (基于您的截图)
        sample_package = {
            'tracking_number': '9300110622200479930771',
            'sender': {
                'name': 'HELEN',
                'address': '4515 LITTLE JOHN ST',
                'city_state_zip': 'BALDWIN PARK CA 91706'
            },
            'recipient': {
                'name': 'NORMA SANCHEZ', 
                'address': '549 RAMOS CT',
                'city_state_zip': 'MILPITAS CA 95035'
            }
        }
        
        # 生成图片标签
        print("正在生成USPS风格的运单标签...")
        img = self.generate_usps_label_image(sample_package)
        img_filename = os.path.join(output_dir, f"usps_label_{sample_package['tracking_number']}.png")
        img.save(img_filename, 'PNG', dpi=(300, 300))
        print(f"✅ 运单标签已保存为: {img_filename}")
        
        return img_filename


# 主程序
if __name__ == "__main__":
    print("=== USPS运单标签生成器 ===")
    generator = SimpleShippingLabelGenerator()
    result = generator.process_packages()
    
    if result:
        print(f"\n🎉 成功生成运单标签!")
        print(f"📁 文件位置: {result}")
        print("\n💡 提示:")
        print("1. 图片尺寸为4x6英寸，300 DPI，适合打印")
        print("2. 包含了USPS标准格式的所有元素")
        print("3. 如需修改信息，请编辑代码中的sample_package变量")
