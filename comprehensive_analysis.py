#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全面的物流异常数据分析脚本
检查所有可能的异常标识字段
"""

import json
import sys

def check_all_fields(obj, path="", abnormal_fields=None):
    """递归检查所有字段，寻找可能的异常标识"""
    if abnormal_fields is None:
        abnormal_fields = []
    
    if isinstance(obj, dict):
        for key, value in obj.items():
            current_path = f"{path}.{key}" if path else key
            
            # 检查可能的异常标识字段
            if value is not None and value != "" and value != []:
                # 检查包含异常、错误、失败等关键词的字段
                if any(keyword in key.lower() for keyword in ['abnormal', 'error', 'fail', 'exception', 'warn', 'alert', 'problem', 'issue']):
                    abnormal_fields.append((current_path, value))
                
                # 检查特定的异常标识字段
                elif key in ['trackAbnormalFlag', 'suspectedProviderName', 'assistantEditBeforeProviderName', 
                           'assistantEditBeforeTrackingNumber', 'logisticChangesType', 'forwardReturnWaybillSn',
                           'failReasonText', 'solutionText', 'applyRefundTime', 'autoRefundTime',
                           'unpaidShippingAutoRefundTime', 'unpaidShippingRefundTime']:
                    abnormal_fields.append((current_path, value))
                
                # 检查布尔值为True的可能异常字段
                elif isinstance(value, bool) and value and any(keyword in key.lower() for keyword in ['refund', 'cancel', 'restrict', 'disable', 'block']):
                    abnormal_fields.append((current_path, value))
            
            # 递归检查嵌套对象
            if isinstance(value, (dict, list)):
                check_all_fields(value, current_path, abnormal_fields)
    
    elif isinstance(obj, list):
        for i, item in enumerate(obj):
            current_path = f"{path}[{i}]" if path else f"[{i}]"
            check_all_fields(item, current_path, abnormal_fields)
    
    return abnormal_fields

def analyze_comprehensive(file_path):
    """全面分析物流异常数据"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
    except Exception as e:
        print(f"读取文件失败: {e}")
        return
    
    # 获取基本信息
    total_items = data['result']['totalItemNum']
    page_items = data['result']['pageItems']
    
    print(f"=== 全面物流异常数据分析报告 ===")
    print(f"总订单数量: {total_items}")
    print(f"当前页面订单数量: {len(page_items)}")
    print()
    
    # 分析每个订单
    all_abnormal_fields = []
    
    for i, item in enumerate(page_items, 1):
        parent_order_sn = item['parentOrderMap']['parentOrderSn']
        
        print(f"--- 订单 {i}: {parent_order_sn} ---")
        
        # 检查所有可能的异常字段
        abnormal_fields = check_all_fields(item)
        
        if abnormal_fields:
            print(f"  发现 {len(abnormal_fields)} 个可能的异常标识:")
            for field_path, value in abnormal_fields:
                print(f"    {field_path}: {value}")
            all_abnormal_fields.extend([(parent_order_sn, field_path, value) for field_path, value in abnormal_fields])
        else:
            print(f"  未发现异常标识")
        
        print()
    
    # 汇总所有异常字段
    print("=== 所有异常字段汇总 ===")
    if all_abnormal_fields:
        # 按字段名分组
        field_groups = {}
        for order_sn, field_path, value in all_abnormal_fields:
            field_name = field_path.split('.')[-1]  # 获取字段名
            if field_name not in field_groups:
                field_groups[field_name] = []
            field_groups[field_name].append((order_sn, value))
        
        for field_name, occurrences in field_groups.items():
            print(f"\n字段: {field_name}")
            print(f"出现次数: {len(occurrences)}")
            for order_sn, value in occurrences:
                print(f"  订单 {order_sn}: {value}")
    else:
        print("未发现任何异常字段")
    
    # 统计有异常标识的订单
    abnormal_orders = set()
    for order_sn, _, _ in all_abnormal_fields:
        abnormal_orders.add(order_sn)
    
    print(f"\n=== 最终统计 ===")
    print(f"有异常标识的订单数量: {len(abnormal_orders)}")
    if abnormal_orders:
        print("异常订单列表:")
        for order_sn in sorted(abnormal_orders):
            print(f"  {order_sn}")

if __name__ == "__main__":
    file_path = "物流异常.json"
    analyze_comprehensive(file_path)
