#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
物流异常数据分析脚本
分析物流异常.json文件，找出所有物流异常的订单
"""

import json
import sys

def analyze_logistics_data(file_path):
    """分析物流异常数据"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
    except Exception as e:
        print(f"读取文件失败: {e}")
        return
    
    # 获取基本信息
    total_items = data['result']['totalItemNum']
    page_items = data['result']['pageItems']
    
    print(f"=== 物流异常数据分析报告 ===")
    print(f"总订单数量: {total_items}")
    print(f"当前页面订单数量: {len(page_items)}")
    print()
    
    # 分析每个订单
    normal_orders = []
    abnormal_orders = []
    
    for i, item in enumerate(page_items, 1):
        parent_order_sn = item['parentOrderMap']['parentOrderSn']
        waybill_info_list = item['parentOrderMap']['waybillInfoList']
        
        print(f"--- 订单 {i}: {parent_order_sn} ---")
        
        # 检查每个运单信息
        has_abnormal = False
        for waybill in waybill_info_list:
            tracking_number = waybill['trackingNumber']
            ship_company_name = waybill['shipCompanyName']
            track_abnormal_info = waybill['trackAbnormalInfo']
            
            print(f"  运单号: {tracking_number}")
            print(f"  物流公司: {ship_company_name}")
            
            # 检查物流异常标识
            track_abnormal_flag = track_abnormal_info.get('trackAbnormalFlag')
            suspected_provider_name = track_abnormal_info.get('suspectedProviderName')
            assistant_edit_before_provider_name = track_abnormal_info.get('assistantEditBeforeProviderName')
            assistant_edit_before_tracking_number = track_abnormal_info.get('assistantEditBeforeTrackingNumber')
            logistic_changes_type = track_abnormal_info.get('logisticChangesType')
            forward_return_waybill_sn = track_abnormal_info.get('forwardReturnWaybillSn')
            
            fail_reason_text = waybill.get('failReasonText')
            solution_text = waybill.get('solutionText')
            
            # 检查所有可能的异常标识字段
            abnormal_indicators = []

            if track_abnormal_flag is not None and track_abnormal_flag != 0:
                abnormal_indicators.append(f"trackAbnormalFlag: {track_abnormal_flag}")
            if suspected_provider_name is not None:
                abnormal_indicators.append(f"suspectedProviderName: {suspected_provider_name}")
            if assistant_edit_before_provider_name is not None:
                abnormal_indicators.append(f"assistantEditBeforeProviderName: {assistant_edit_before_provider_name}")
            if assistant_edit_before_tracking_number is not None:
                abnormal_indicators.append(f"assistantEditBeforeTrackingNumber: {assistant_edit_before_tracking_number}")
            if logistic_changes_type is not None:
                abnormal_indicators.append(f"logisticChangesType: {logistic_changes_type}")
            if forward_return_waybill_sn is not None:
                abnormal_indicators.append(f"forwardReturnWaybillSn: {forward_return_waybill_sn}")
            if fail_reason_text is not None and fail_reason_text.strip() != "":
                abnormal_indicators.append(f"failReasonText: {fail_reason_text}")
            if solution_text is not None and solution_text.strip() != "":
                abnormal_indicators.append(f"solutionText: {solution_text}")

            # 检查其他可能的异常字段
            apply_refund_time = waybill.get('applyRefundTime')
            auto_refund_time = waybill.get('autoRefundTime')
            unpaid_shipping_auto_refund_time = waybill.get('unpaidShippingAutoRefundTime')
            unpaid_shipping_refund_time = waybill.get('unpaidShippingRefundTime')
            include_refund_order = waybill.get('includeRefundOrder', False)

            if apply_refund_time is not None:
                abnormal_indicators.append(f"applyRefundTime: {apply_refund_time}")
            if auto_refund_time is not None:
                abnormal_indicators.append(f"autoRefundTime: {auto_refund_time}")
            if unpaid_shipping_auto_refund_time is not None:
                abnormal_indicators.append(f"unpaidShippingAutoRefundTime: {unpaid_shipping_auto_refund_time}")
            if unpaid_shipping_refund_time is not None:
                abnormal_indicators.append(f"unpaidShippingRefundTime: {unpaid_shipping_refund_time}")
            if include_refund_order:
                abnormal_indicators.append(f"includeRefundOrder: {include_refund_order}")

            # 判断是否有异常
            if abnormal_indicators:
                
                has_abnormal = True
                print(f"  *** 发现物流异常 ***")
                for indicator in abnormal_indicators:
                    print(f"    {indicator}")
            else:
                print(f"  状态: 正常")
        
        if has_abnormal:
            abnormal_orders.append({
                'parentOrderSn': parent_order_sn,
                'index': i
            })
        else:
            normal_orders.append({
                'parentOrderSn': parent_order_sn,
                'index': i
            })
        
        print()
    
    # 输出汇总信息
    print("=== 汇总信息 ===")
    print(f"正常订单数量: {len(normal_orders)}")
    print(f"异常订单数量: {len(abnormal_orders)}")
    print()
    
    if abnormal_orders:
        print("=== 物流异常订单列表 ===")
        for order in abnormal_orders:
            print(f"订单 {order['index']}: {order['parentOrderSn']}")
        print()
        
        print("=== 用于区分物流异常订单的字段 ===")
        print("主要字段: trackAbnormalFlag")
        print("  - 正常订单: trackAbnormalFlag = null")
        print("  - 异常订单: trackAbnormalFlag = 1 (或其他非null值)")
        print()
        print("其他可能的异常标识字段:")
        print("  - suspectedProviderName: 疑似物流商名称")
        print("  - assistantEditBeforeProviderName: 助手编辑前的物流商名称")
        print("  - assistantEditBeforeTrackingNumber: 助手编辑前的运单号")
        print("  - logisticChangesType: 物流变更类型")
        print("  - forwardReturnWaybillSn: 转发退回运单号")
        print("  - failReasonText: 失败原因文本")
        print("  - solutionText: 解决方案文本")
    else:
        print("未发现物流异常订单")

if __name__ == "__main__":
    file_path = "物流异常.json"
    analyze_logistics_data(file_path)
