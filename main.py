#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多店铺Temu待发货订单导出工具 - 重构版
使用模块化架构，支持自动Token管理和配置更新
"""

import sys
import os
import argparse
from typing import Optional

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from config.config_manager import ConfigManager
from core.token_manager import TokenManager
from api.temu_client import TemuAPIClient, StoreManager
from export.multi_store_exporter import MultiStoreExporter
from utils.logger import setup_logger, get_default_log_file


class TemuOrderExportApp:
    """Temu订单导出应用程序"""
    
    def __init__(self, config_file: str = "config.json"):
        """
        初始化应用程序
        
        Args:
            config_file: 配置文件路径
        """
        # 设置日志
        self.logger = setup_logger(__name__, log_file=get_default_log_file())
        
        # 初始化组件
        self.config = ConfigManager(config_file)
        self.token_manager = TokenManager(self.config)
        self.api_client = TemuAPIClient(self.config, self.token_manager)
        self.store_manager = StoreManager(self.config, self.token_manager)
        self.exporter = None
    
    def run_health_check(self) -> bool:
        """
        运行健康检查
        
        Returns:
            bool: 检查是否通过
        """
        self.logger.info("🔍 开始系统健康检查...")
        
        try:
            # Token健康检查
            health_result = self.token_manager.run_health_check()
            
            # 显示检查结果
            self.logger.info(f"检查时间: {health_result['check_time']}")
            for message in health_result['messages']:
                self.logger.info(f"  {message}")
            
            overall_success = health_result['overall_status'] == '正常'
            
            if overall_success:
                self.logger.info("✅ 系统健康检查通过")
            else:
                self.logger.warning("⚠️ 系统健康检查发现问题")
                
                # 检查是否需要警告
                should_alert, alert_message = self.token_manager.should_alert()
                if should_alert:
                    self.logger.warning(f"🚨 警告: {alert_message}")
            
            return overall_success
            
        except Exception as e:
            self.logger.error(f"❌ 健康检查失败: {e}")
            return False
    
    def discover_stores(self) -> bool:
        """
        发现可用店铺
        
        Returns:
            bool: 是否成功
        """
        self.logger.info("🔍 开始发现可用店铺...")
        
        try:
            success, stores = self.store_manager.discover_stores()
            
            if success:
                self.logger.info(f"✅ 发现 {len(stores)} 个可用店铺:")
                for i, store in enumerate(stores, 1):
                    self.logger.info(f"   {i}. {store['mall_name']} (ID: {store['mall_id']})")
                return True
            else:
                self.logger.error(f"❌ 发现店铺失败: {stores}")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ 发现店铺时出错: {e}")
            return False
    
    def export_orders(self, output_dir: str = "多店铺待发货订单", 
                     max_workers: int = 3, auto_confirm: bool = False) -> bool:
        """
        导出订单
        
        Args:
            output_dir: 输出目录
            max_workers: 最大并发数
            auto_confirm: 是否自动确认
            
        Returns:
            bool: 是否成功
        """
        try:
            # 获取可用店铺
            stores = self.store_manager.get_available_stores()
            
            if not stores:
                self.logger.error("❌ 未发现任何可用店铺")
                return False
            
            self.logger.info(f"\n📋 发现店铺列表 ({len(stores)}个):")
            for i, store in enumerate(stores, 1):
                self.logger.info(f"   {i}. {store['mall_name']} (ID: {store['mall_id']})")
            
            # 确认开始处理
            if not auto_confirm:
                confirm = input(f"\n是否开始导出所有店铺的待发货订单？(y/n): ").strip().lower()
                if confirm != 'y':
                    self.logger.info("❌ 用户取消操作")
                    return False
            
            # 创建导出器
            self.exporter = MultiStoreExporter(self.api_client, output_dir)
            
            # 开始导出
            results, all_orders = self.exporter.export_all_stores(stores, max_workers)
            
            # 生成汇总文件
            summary_files = self.exporter.generate_summary_files(results, all_orders)
            
            # 生成汇总报告
            self.exporter.generate_summary_report(results, all_orders, summary_files)
            
            # 检查是否有成功的导出
            successful_exports = [r for r in results if r['success']]
            return len(successful_exports) > 0
            
        except Exception as e:
            self.logger.error(f"❌ 导出订单时出错: {e}")
            return False
    
    def run(self, args):
        """
        运行应用程序
        
        Args:
            args: 命令行参数
        """
        self.logger.info("🚀 多店铺Temu待发货订单导出工具 - 重构版")
        self.logger.info("=" * 60)
        self.logger.info("自动获取所有店铺并导出待发货订单")
        self.logger.info("=" * 60)
        
        try:
            # 1. 健康检查
            if not args.skip_health_check:
                if not self.run_health_check():
                    if not args.force:
                        self.logger.error("❌ 健康检查未通过，使用 --force 强制继续")
                        return False
                    else:
                        self.logger.warning("⚠️ 健康检查未通过，但强制继续执行")
            
            # 2. 发现店铺（如果需要）
            if args.discover_stores:
                if not self.discover_stores():
                    return False
            
            # 3. 导出订单
            if args.export:
                success = self.export_orders(
                    output_dir=args.output_dir,
                    max_workers=args.max_workers,
                    auto_confirm=args.auto_confirm
                )
                
                if success:
                    self.logger.info("🎉 导出完成!")
                    return True
                else:
                    self.logger.error("❌ 导出失败")
                    return False
            
            return True
            
        except KeyboardInterrupt:
            self.logger.info("❌ 用户中断操作")
            return False
        except Exception as e:
            self.logger.error(f"❌ 运行时出错: {e}")
            return False
        finally:
            # 清理资源
            try:
                if hasattr(self, 'token_manager'):
                    self.token_manager.disconnect_browser()
                if hasattr(self, 'store_manager'):
                    self.store_manager.disconnect_browser()
            except:
                pass


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='多店铺Temu待发货订单导出工具')
    
    # 基本选项
    parser.add_argument('--config', default='config.json', 
                       help='配置文件路径 (默认: config.json)')
    parser.add_argument('--output-dir', default='多店铺待发货订单',
                       help='输出目录 (默认: 多店铺待发货订单)')
    parser.add_argument('--max-workers', type=int, default=3,
                       help='最大并发数 (默认: 3)')
    
    # 操作选项
    parser.add_argument('--health-check', action='store_true',
                       help='只执行健康检查')
    parser.add_argument('--discover-stores', action='store_true',
                       help='发现并缓存可用店铺')
    parser.add_argument('--export', action='store_true', default=True,
                       help='导出订单 (默认操作)')
    
    # 控制选项
    parser.add_argument('--skip-health-check', action='store_true',
                       help='跳过健康检查')
    parser.add_argument('--auto-confirm', action='store_true',
                       help='自动确认，不询问用户')
    parser.add_argument('--force', action='store_true',
                       help='强制执行，即使健康检查失败')
    
    args = parser.parse_args()
    
    # 如果只指定了健康检查，则不执行导出
    if args.health_check:
        args.export = False
    
    # 创建应用程序实例
    app = TemuOrderExportApp(args.config)
    
    # 运行应用程序
    success = app.run(args)
    
    # 退出
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
