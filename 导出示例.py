#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据导出示例
展示如何使用通用数据导出工具和订单数据导出工具
"""

from 通用数据导出工具 import UniversalDataExporter
from 订单数据导出工具 import OrderDataExporter
from datetime import datetime
import pandas as pd


def example_universal_export():
    """通用数据导出示例"""
    print("=" * 60)
    print("🔄 通用数据导出示例")
    print("=" * 60)
    
    # 创建示例数据
    sample_data = [
        {'编号': 1, '姓名': '张三', '年龄': 25, '城市': '北京', '职业': '工程师', '薪资': 15000},
        {'编号': 2, '姓名': '李四', '年龄': 30, '城市': '上海', '职业': '设计师', '薪资': 12000},
        {'编号': 3, '姓名': '王五', '年龄': 28, '城市': '广州', '职业': '产品经理', '薪资': 18000},
        {'编号': 4, '姓名': '赵六', '年龄': 32, '城市': '深圳', '职业': '数据分析师', '薪资': 16000},
        {'编号': 5, '姓名': '钱七', '年龄': 27, '城市': '杭州', '职业': '前端开发', '薪资': 14000},
    ]
    
    # 创建导出器
    exporter = UniversalDataExporter()
    
    # 导出Excel文件
    excel_file = exporter.export_to_excel(
        data=sample_data,
        filename="员工信息导出.xlsx",
        sheet_name="员工信息",
        apply_formatting=True,
        create_summary=True
    )
    
    # 导出CSV文件
    csv_file = exporter.export_to_csv(
        data=sample_data,
        filename="员工信息导出.csv"
    )
    
    print(f"\n✅ 通用导出完成:")
    print(f"   Excel文件: {excel_file}")
    print(f"   CSV文件: {csv_file}")


def example_multi_sheet_export():
    """多工作表导出示例"""
    print("\n" + "=" * 60)
    print("🔄 多工作表导出示例")
    print("=" * 60)
    
    # 创建多个数据表
    employees = [
        {'编号': 1, '姓名': '张三', '部门': '技术部', '职位': '工程师'},
        {'编号': 2, '姓名': '李四', '部门': '设计部', '职位': '设计师'},
        {'编号': 3, '姓名': '王五', '部门': '产品部', '职位': '产品经理'},
    ]
    
    departments = [
        {'部门编号': 'T001', '部门名称': '技术部', '负责人': '张三', '人数': 10},
        {'部门编号': 'D001', '部门名称': '设计部', '负责人': '李四', '人数': 5},
        {'部门编号': 'P001', '部门名称': '产品部', '负责人': '王五', '人数': 3},
    ]
    
    projects = [
        {'项目编号': 'PRJ001', '项目名称': '电商平台', '状态': '进行中', '负责人': '张三'},
        {'项目编号': 'PRJ002', '项目名称': '移动应用', '状态': '已完成', '负责人': '李四'},
        {'项目编号': 'PRJ003', '项目名称': '数据分析', '状态': '计划中', '负责人': '王五'},
    ]
    
    # 创建导出器
    exporter = UniversalDataExporter()
    
    # 准备多工作表数据
    sheets_data = {
        '员工信息': employees,
        '部门信息': departments,
        '项目信息': projects
    }
    
    # 导出多工作表Excel文件
    multi_file = exporter.export_multi_sheet_excel(
        data_dict=sheets_data,
        filename="公司信息汇总.xlsx",
        apply_formatting=True
    )
    
    print(f"\n✅ 多工作表导出完成:")
    print(f"   文件: {multi_file}")


def example_order_export():
    """订单数据导出示例"""
    print("\n" + "=" * 60)
    print("🔄 订单数据导出示例")
    print("=" * 60)
    
    # 创建示例订单数据（模拟真实的订单结构）
    sample_orders = [
        {
            '店铺ID': '634418217955942',
            '店铺名称': '测试店铺A',
            '父订单号': 'PO-211-13196496056952863',
            '子订单号': '211-13196506542712863',
            '订单时间': '2025-01-19 14:02:38',
            '确认时间': '2025-01-19 14:02:40',
            '最晚发货时间': '2025-01-22 13:29:59',
            '剩余发货时间': '2天19小时',
            '预计送达时间': '2025-01-29 12:59:59',
            '商品名称': '测试商品A - 植物主题T恤',
            '商品规格': 'Black / Label size: L',
            'SKU ID': '17592946181757',
            '商品ID': '601099708867895',
            '数量': 1,
            '未发货数量': 1,
            '已发货数量': 0,
            '包裹号': '',
            '快递单号': '',
            '快递公司': '',
            '发货仓库': '',
            '收货国家': '美国',
            '收货州/省': 'TX',
            '收货城市': 'SAN ANTONIO',
            '站点': '美国站',
            '父订单状态': '2',
            '订单状态': '2',
            '包裹状态': '1',
            '订单包裹状态': '1',
            '仓库名称': 'DHLeCommerce - 300g以上',
            '是否COD订单': '否',
            '商品缩略图': 'https://img.kwcdn.com/thumbnail/s/test.jpeg',
            '产品SKU列表': '9004405499',
            '扩展代码': 'TSM-1012ZM-430-Black-L'
        },
        {
            '店铺ID': '634418217955942',
            '店铺名称': '测试店铺A',
            '父订单号': 'PO-211-13167599826552811',
            '子订单号': '211-13167609001592811',
            '订单时间': '2025-01-19 12:32:45',
            '确认时间': '2025-01-19 12:32:45',
            '最晚发货时间': '2025-01-21 15:30:00',
            '剩余发货时间': '1天21小时',
            '预计送达时间': '2025-01-28 14:59:59',
            '商品名称': '测试商品B - 狮子主题T恤',
            '商品规格': 'Black / Label size: XXL',
            'SKU ID': '17592915514307',
            '商品ID': '601099701337892',
            '数量': 1,
            '未发货数量': 1,
            '已发货数量': 0,
            '包裹号': 'PK-4154955888066690614',
            '快递单号': '9300110622200047993545',
            '快递公司': 'USPS',
            '发货仓库': 'S2B - 洛杉矶一厂',
            '收货国家': '美国',
            '收货州/省': 'CA',
            '收货城市': 'FONTANA',
            '站点': '美国站',
            '父订单状态': '2',
            '订单状态': '2',
            '包裹状态': '1',
            '订单包裹状态': '1',
            '仓库名称': 'USPS - 皇后长岛 - 工厂投递给邮局 - 300g以下',
            '是否COD订单': '否',
            '商品缩略图': 'https://img.kwcdn.com/thumbnail/s/test2.jpeg',
            '产品SKU列表': '6191044191',
            '扩展代码': 'TSM-1008ZM-116-Black-XXL'
        }
    ]
    
    # 创建第二个店铺的订单数据
    store2_orders = [
        {
            '店铺ID': '634418218259845',
            '店铺名称': '测试店铺B',
            '父订单号': 'PO-211-13204848169592845',
            '子订单号': '211-13204866519672845',
            '订单时间': '2025-01-19 08:49:18',
            '确认时间': '2025-01-19 08:49:22',
            '最晚发货时间': '2025-01-21 12:29:59',
            '剩余发货时间': '1天18小时',
            '预计送达时间': '2025-01-28 11:59:59',
            '商品名称': '测试商品C - 椒盐脆饼主题T恤',
            '商品规格': 'Black / Label size: M',
            'SKU ID': '17596354758240',
            '商品ID': '601100556734408',
            '数量': 1,
            '未发货数量': 1,
            '已发货数量': 0,
            '包裹号': 'PK-4154954367631490614',
            '快递单号': '9300110622200047990766',
            '快递公司': 'USPS',
            '发货仓库': 'S2B - 洛杉矶一厂',
            '收货国家': '美国',
            '收货州/省': 'NY',
            '收货城市': 'RENSSELAER',
            '站点': '美国站',
            '父订单状态': '2',
            '订单状态': '2',
            '包裹状态': '1',
            '订单包裹状态': '1',
            '仓库名称': '面单地址5 USPS',
            '是否COD订单': '否',
            '商品缩略图': 'https://img.kwcdn.com/thumbnail/s/test3.jpeg',
            '产品SKU列表': '38571573000',
            '扩展代码': 'MTBMD-17622299-black-M'
        }
    ]
    
    # 创建订单导出器
    order_exporter = OrderDataExporter()
    
    # 导出单店铺订单
    print("📋 导出单店铺订单...")
    single_result = order_exporter.export_single_store_orders(
        orders=sample_orders,
        store_id='634418217955942',
        store_name='测试店铺A',
        output_dir="示例订单导出"
    )
    
    if single_result['success']:
        print(f"   ✅ 单店铺导出成功: {single_result['filename']}")
    
    # 导出多店铺订单
    print("\n📋 导出多店铺订单...")
    multi_store_data = {
        '测试店铺A': sample_orders,
        '测试店铺B': store2_orders
    }
    
    multi_result = order_exporter.export_multi_store_orders(
        store_orders_dict=multi_store_data,
        output_dir="示例多店铺订单导出",
        create_summary=True
    )
    
    # 打印导出结果摘要
    order_exporter.print_export_summary(multi_result)


def main():
    """主函数 - 运行所有示例"""
    print("🚀 数据导出工具示例")
    print("展示如何使用通用数据导出工具和订单数据导出工具")
    print("格式与订单导出2025719002.xlsx保持一致")
    print("=" * 80)
    
    try:
        # 运行通用导出示例
        example_universal_export()
        
        # 运行多工作表导出示例
        example_multi_sheet_export()
        
        # 运行订单导出示例
        example_order_export()
        
        print("\n" + "=" * 80)
        print("🎉 所有示例运行完成!")
        print("请查看生成的Excel和CSV文件")
        print("=" * 80)
        
    except Exception as e:
        print(f"\n❌ 运行示例时发生错误: {str(e)}")
        print("请检查依赖包是否正确安装")


if __name__ == "__main__":
    main()
