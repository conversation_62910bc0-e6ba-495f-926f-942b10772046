import requests
import json
import os
from datetime import datetime
from reportlab.lib.pagesizes import letter
from reportlab.pdfgen import canvas
from reportlab.lib.units import inch
from reportlab.graphics.barcode import code128
from reportlab.graphics import renderPDF
from reportlab.graphics.shapes import Drawing
from PIL import Image, ImageDraw, ImageFont
import qrcode


class TemuShippingLabelGenerator:
    def __init__(self):
        self.headers = {
            "accept": "application/json, text/plain, */*",
            "accept-language": "zh-CN,zh;q=0.9",
            "content-type": "application/json;charset=UTF-8",
            "mallid": "634418218777560",
            "origin": "https://agentseller-us.temu.com",
            "priority": "u=1, i",
            "referer": "https://agentseller-us.temu.com/mmsos/waybill.html?logistics_create_time=1750003200000%2C1752681599999&page_number=2&page_size=200&sort_type=1&call_begin_time=1750003200000&call_end_time=1752681599999&active_waybill_tab=0",
            "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"",
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": "\"Windows\"",
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "same-origin",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36",
            "x-document-referer": "https://agentseller-us.temu.com/main/authentication?redirectUrl=https%3A%2F%2Fagentseller-us.temu.com%2Fmmsos%2Fwaybill.html",
            "x-phan-data": "0aeJx7xMxiYPiIOSza0NzUyMzU3MDU1NzMWAfGszQwNjBB8AwNDUxjAT7WC4M"
        }
        self.cookies = {
            "api_uid": "CiCFY2h2Gge8HQBGEdGIAg==",
            "_nano_fp": "XpmyX5d8X0X8nqTxX9_JX7Kd7W6yX~XlC1RvKXhc",
            "_bee": "Guoq8zmpMw54MRLiHdZETgLhuZwVQapH",
            "njrpl": "Guoq8zmpMw54MRLiHdZETgLhuZwVQapH",
            "dilx": "8Y5zRfV0WSOxkuYUB-wow",
            "hfsc": "L3yPeow36T3/2p7PeQ==",
            "timezone": "Asia%2FShanghai",
            "webp": "1",
            "mallid": "634418218777560",
            "region": "0",
            "seller_temp": "N_eyJ0IjoiUU9oQ2pyS2FRUW9MenJrbkFzODNYVXRpdWJFZ2dVekU4bHA4UUtpemRxSEV2K2R4VzczS1lWWlFOV2tGUEVWR1U5WDl6K3V1QW1CZTdoQVQvQnVnYkE9PSIsInYiOjEsInMiOjEwMDAxLCJ1IjoyMzUxNDQ1MzE4NDUyMX0="
        }

    def get_package_data(self):
        """获取包裹数据"""
        url = "https://agentseller-us.temu.com/mms/eagle/package/main_batch_query"
        data = {
            "logistics_create_time": [1750003200000, 1752681599999],
            "page_number": 1,
            "page_size": 200,
            "sort_type": 1,
            "call_begin_time": 1750003200,
            "call_end_time": 1752681599
        }

        try:
            response = requests.post(
                url,
                headers=self.headers,
                cookies=self.cookies,
                data=json.dumps(data, separators=(',', ':'))
            )

            if response.status_code == 200:
                return response.json()
            else:
                print(f"API请求失败，状态码: {response.status_code}")
                return None

        except Exception as e:
            print(f"请求异常: {e}")
            return None

    def get_shipping_label_pdf(self, tracking_number):
        """尝试获取运单PDF文件"""
        # 这里需要根据实际的API端点来获取PDF
        # 通常会有专门的API来获取运单PDF
        pdf_url = f"https://agentseller-us.temu.com/mms/eagle/package/shipping_label/{tracking_number}"

        try:
            response = requests.get(pdf_url, headers=self.headers, cookies=self.cookies)
            if response.status_code == 200 and response.headers.get('content-type') == 'application/pdf':
                return response.content
        except Exception as e:
            print(f"获取PDF失败: {e}")

        return None

    def generate_usps_label_image(self, package_info):
        """生成USPS风格的运单标签图片"""
        # 创建图片 (4x6英寸，300 DPI)
        width, height = 1200, 1800
        img = Image.new('RGB', (width, height), 'white')
        draw = ImageDraw.Draw(img)

        try:
            # 尝试加载字体
            title_font = ImageFont.truetype("arial.ttf", 36)
            header_font = ImageFont.truetype("arial.ttf", 24)
            text_font = ImageFont.truetype("arial.ttf", 18)
            small_font = ImageFont.truetype("arial.ttf", 14)
        except:
            # 如果无法加载字体，使用默认字体
            title_font = ImageFont.load_default()
            header_font = ImageFont.load_default()
            text_font = ImageFont.load_default()
            small_font = ImageFont.load_default()

        # USPS标题区域
        draw.rectangle([0, 0, width, 120], fill='white', outline='black', width=2)

        # USPS Logo区域 (简化版)
        draw.rectangle([20, 20, 200, 100], fill='navy')
        draw.text((30, 40), "USPS", fill='white', font=title_font)

        # APIs标题
        draw.text((width-200, 30), "USPS APIs", fill='black', font=header_font)

        # 邮资已付区域
        draw.rectangle([width-300, 60, width-20, 100], fill='lightgray', outline='black')
        draw.text((width-280, 70), "U.S. POSTAGE PAID", fill='black', font=small_font)

        # Ground Advantage标题
        y_pos = 140
        draw.text((20, y_pos), "USPS GROUND ADVANTAGE™", fill='black', font=header_font)

        # 发件人信息
        y_pos += 60
        sender_info = package_info.get('sender', {})
        draw.text((20, y_pos), f"FROM:", fill='black', font=text_font)
        y_pos += 25
        draw.text((20, y_pos), sender_info.get('name', 'HELEN'), fill='black', font=text_font)
        y_pos += 25
        draw.text((20, y_pos), sender_info.get('address', '4515 LITTLE JOHN ST'), fill='black', font=text_font)
        y_pos += 25
        draw.text((20, y_pos), sender_info.get('city_state_zip', 'BALDWIN PARK CA 91706'), fill='black', font=text_font)

        # RDC信息
        draw.text((width-150, y_pos-50), "RDC 01", fill='black', font=header_font)

        # 收件人信息
        y_pos += 80
        draw.rectangle([0, y_pos-10, width, y_pos+150], fill='white', outline='black', width=2)

        recipient_info = package_info.get('recipient', {})
        draw.text((20, y_pos+10), recipient_info.get('name', 'NORMA SANCHEZ'), fill='black', font=header_font)
        y_pos += 40
        draw.text((20, y_pos), recipient_info.get('address', '549 RAMOS CT'), fill='black', font=text_font)
        y_pos += 25
        draw.text((20, y_pos), recipient_info.get('city_state_zip', 'MILPITAS CA 95035'), fill='black', font=text_font)

        # 二维码 (左下角)
        y_pos += 60
        qr = qrcode.QRCode(version=1, box_size=3, border=1)
        qr.add_data(package_info.get('tracking_number', '9300110622200479930771'))
        qr.make(fit=True)
        qr_img = qr.make_image(fill_color="black", back_color="white")
        qr_img = qr_img.resize((120, 120))
        img.paste(qr_img, (20, y_pos))

        # 追踪号码标题
        y_pos += 140
        draw.text((20, y_pos), "USPS TRACKING # USPS Ship", fill='black', font=text_font)

        # 追踪号码条形码
        y_pos += 30
        tracking_number = package_info.get('tracking_number', '9300110622200479930771')

        # 简化的条形码绘制
        barcode_width = width - 40
        bar_width = barcode_width // len(tracking_number)

        for i, char in enumerate(tracking_number):
            x = 20 + i * bar_width
            if int(char) % 2 == 0:  # 简化的条形码逻辑
                draw.rectangle([x, y_pos, x + bar_width//2, y_pos + 60], fill='black')

        # 追踪号码文本
        y_pos += 70
        draw.text((20, y_pos), tracking_number, fill='black', font=header_font)

        # 右下角二维码
        qr2 = qrcode.QRCode(version=1, box_size=2, border=1)
        qr2.add_data("USPS")
        qr2.make(fit=True)
        qr2_img = qr2.make_image(fill_color="black", back_color="white")
        qr2_img = qr2_img.resize((80, 80))
        img.paste(qr2_img, (width-100, height-100))

        return img

    def generate_pdf_label(self, package_info, filename):
        """生成PDF格式的运单标签"""
        c = canvas.Canvas(filename, pagesize=letter)
        width, height = letter

        # USPS标题
        c.setFont("Helvetica-Bold", 24)
        c.drawString(50, height-50, "USPS GROUND ADVANTAGE™")

        # 发件人信息
        c.setFont("Helvetica", 12)
        y_pos = height - 100
        sender_info = package_info.get('sender', {})
        c.drawString(50, y_pos, f"FROM: {sender_info.get('name', 'HELEN')}")
        c.drawString(50, y_pos-20, sender_info.get('address', '4515 LITTLE JOHN ST'))
        c.drawString(50, y_pos-40, sender_info.get('city_state_zip', 'BALDWIN PARK CA 91706'))

        # 收件人信息
        y_pos -= 100
        c.setFont("Helvetica-Bold", 14)
        recipient_info = package_info.get('recipient', {})
        c.drawString(50, y_pos, recipient_info.get('name', 'NORMA SANCHEZ'))
        c.setFont("Helvetica", 12)
        c.drawString(50, y_pos-20, recipient_info.get('address', '549 RAMOS CT'))
        c.drawString(50, y_pos-40, recipient_info.get('city_state_zip', 'MILPITAS CA 95035'))

        # 追踪号码
        y_pos -= 100
        tracking_number = package_info.get('tracking_number', '9300110622200479930771')
        c.setFont("Helvetica", 10)
        c.drawString(50, y_pos, "USPS TRACKING # USPS Ship")

        # 条形码 (使用Code128)
        try:
            from reportlab.graphics.barcode import code128
            barcode = code128.Code128(tracking_number)
            barcode.drawOn(c, 50, y_pos-60)
        except:
            # 如果条形码库不可用，只显示文本
            c.setFont("Helvetica-Bold", 16)
            c.drawString(50, y_pos-40, tracking_number)

        c.save()
        print(f"PDF标签已保存为: {filename}")

    def process_packages(self):
        """处理包裹数据并生成标签"""
        print("正在获取包裹数据...")
        package_data = self.get_package_data()

        if not package_data:
            print("无法获取包裹数据")
            return

        print("API响应:")
        print(json.dumps(package_data, indent=2, ensure_ascii=False))

        # 创建输出目录
        output_dir = "shipping_labels"
        os.makedirs(output_dir, exist_ok=True)

        # 示例包裹信息 (基于您的截图)
        sample_package = {
            'tracking_number': '9300110622200479930771',
            'sender': {
                'name': 'HELEN',
                'address': '4515 LITTLE JOHN ST',
                'city_state_zip': 'BALDWIN PARK CA 91706'
            },
            'recipient': {
                'name': 'NORMA SANCHEZ',
                'address': '549 RAMOS CT',
                'city_state_zip': 'MILPITAS CA 95035'
            }
        }

        # 生成图片标签
        print("正在生成图片标签...")
        img = self.generate_usps_label_image(sample_package)
        img_filename = os.path.join(output_dir, f"label_{sample_package['tracking_number']}.png")
        img.save(img_filename, 'PNG', dpi=(300, 300))
        print(f"图片标签已保存为: {img_filename}")

        # 生成PDF标签
        print("正在生成PDF标签...")
        pdf_filename = os.path.join(output_dir, f"label_{sample_package['tracking_number']}.pdf")
        self.generate_pdf_label(sample_package, pdf_filename)

        # 尝试获取原始PDF
        print("尝试获取原始PDF...")
        original_pdf = self.get_shipping_label_pdf(sample_package['tracking_number'])
        if original_pdf:
            original_filename = os.path.join(output_dir, f"original_{sample_package['tracking_number']}.pdf")
            with open(original_filename, 'wb') as f:
                f.write(original_pdf)
            print(f"原始PDF已保存为: {original_filename}")
        else:
            print("无法获取原始PDF文件")


# 主程序
if __name__ == "__main__":
    generator = TemuShippingLabelGenerator()
    generator.process_packages()