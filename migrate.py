#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
迁移脚本
帮助用户从旧版本迁移到新版本
"""

import os
import json
import shutil
from datetime import datetime


def backup_old_files():
    """备份旧版本文件"""
    print("📦 备份旧版本文件...")
    
    backup_dir = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    os.makedirs(backup_dir, exist_ok=True)
    
    # 需要备份的文件
    files_to_backup = [
        "多店铺待发货订单导出工具.py",
        "待发货订单导出工具.py",
        "订单数据导出工具.py",
        "通用数据导出工具.py",
        "token_monitor.py",
        "config.json"
    ]
    
    backed_up_files = []
    for file in files_to_backup:
        if os.path.exists(file):
            shutil.copy2(file, backup_dir)
            backed_up_files.append(file)
            print(f"  ✅ 已备份: {file}")
    
    if backed_up_files:
        print(f"📁 备份目录: {backup_dir}")
        return backup_dir
    else:
        print("  ℹ️ 没有找到需要备份的文件")
        return None


def migrate_config():
    """迁移配置文件"""
    print("\n⚙️ 迁移配置文件...")
    
    if not os.path.exists("config.json"):
        print("  ℹ️ 未找到现有配置文件，将创建默认配置")
        create_default_config()
        return
    
    try:
        # 读取现有配置
        with open("config.json", 'r', encoding='utf-8') as f:
            old_config = json.load(f)
        
        # 检查配置格式
        if "settings" not in old_config:
            print("  🔄 更新配置文件格式...")
            
            # 添加新的设置项
            old_config["settings"] = {
                "auto_refresh_token": True,
                "token_check_interval": 3600,
                "max_retries": 3,
                "retry_delay": 2,
                "chrome_debug_port": 9337
            }
        
        if "api_endpoints" not in old_config:
            old_config["api_endpoints"] = {
                "orders": "https://agentseller-us.temu.com/kirogi/bg/mms/recentOrderList",
                "user_info": "https://seller.kuajingmaihuo.com/bg/quiet/api/mms/userInfo"
            }
        
        # 保存更新后的配置
        with open("config.json", 'w', encoding='utf-8') as f:
            json.dump(old_config, f, indent=4, ensure_ascii=False)
        
        print("  ✅ 配置文件迁移完成")
        
    except Exception as e:
        print(f"  ❌ 配置文件迁移失败: {e}")
        print("  🔄 创建默认配置文件...")
        create_default_config()


def create_default_config():
    """创建默认配置文件"""
    default_config = {
        "cookies": {},
        "headers": {
            "accept": "application/json, text/plain, */*",
            "accept-language": "zh-CN,zh;q=0.9",
            "content-type": "application/json;charset=UTF-8",
            "origin": "https://agentseller-us.temu.com",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        },
        "settings": {
            "auto_refresh_token": True,
            "token_check_interval": 3600,
            "max_retries": 3,
            "retry_delay": 2,
            "chrome_debug_port": 9337
        },
        "api_endpoints": {
            "orders": "https://agentseller-us.temu.com/kirogi/bg/mms/recentOrderList",
            "user_info": "https://seller.kuajingmaihuo.com/bg/quiet/api/mms/userInfo"
        }
    }
    
    with open("config.json", 'w', encoding='utf-8') as f:
        json.dump(default_config, f, indent=4, ensure_ascii=False)
    
    print("  ✅ 默认配置文件已创建")


def create_directories():
    """创建必要的目录"""
    print("\n📁 创建必要的目录...")
    
    directories = [
        "logs",
        "多店铺待发货订单"
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"  ✅ 目录已创建: {directory}")


def check_dependencies():
    """检查依赖"""
    print("\n📦 检查依赖...")
    
    required_packages = [
        "requests",
        "pandas",
        "openpyxl",
        "selenium"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"  ✅ {package}")
        except ImportError:
            print(f"  ❌ {package} (缺失)")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️ 缺少以下依赖包: {', '.join(missing_packages)}")
        print("请运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True


def test_new_version():
    """测试新版本"""
    print("\n🧪 测试新版本...")
    
    try:
        # 测试导入新模块
        import sys
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))
        
        from config.config_manager import ConfigManager
        from core.token_manager import TokenManager
        from api.temu_client import TemuAPIClient
        
        print("  ✅ 模块导入成功")
        
        # 测试配置管理器
        config = ConfigManager()
        print("  ✅ 配置管理器初始化成功")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 测试失败: {e}")
        return False


def show_usage_guide():
    """显示使用指南"""
    print("\n" + "="*60)
    print("🎉 迁移完成！")
    print("="*60)
    
    print("\n📖 使用指南:")
    print("1. 快速开始:")
    print("   python run.py")
    
    print("\n2. 完整功能:")
    print("   python main.py --help")
    
    print("\n3. Token监控:")
    print("   python monitor_token.py --once")
    
    print("\n4. 查看详细说明:")
    print("   查看 README_新架构.md 文件")
    
    print("\n⚠️ 重要提示:")
    print("- 确保Chrome浏览器已启动调试模式")
    print("- 确保已登录Temu卖家后台")
    print("- 首次运行会自动发现和缓存店铺信息")


def main():
    """主函数"""
    print("🚀 Temu订单导出工具迁移脚本")
    print("=" * 50)
    print("将帮助您从旧版本迁移到新的模块化架构")
    print("=" * 50)
    
    try:
        # 1. 备份旧文件
        backup_dir = backup_old_files()
        
        # 2. 迁移配置
        migrate_config()
        
        # 3. 创建目录
        create_directories()
        
        # 4. 检查依赖
        if not check_dependencies():
            print("\n❌ 请先安装缺失的依赖包")
            return
        
        # 5. 测试新版本
        if not test_new_version():
            print("\n❌ 新版本测试失败，请检查安装")
            return
        
        # 6. 显示使用指南
        show_usage_guide()
        
        if backup_dir:
            print(f"\n💾 旧版本文件已备份到: {backup_dir}")
        
    except Exception as e:
        print(f"\n❌ 迁移过程中出错: {e}")
        return
    
    print("\n✅ 迁移完成！现在可以使用新版本了。")


if __name__ == "__main__":
    main()
