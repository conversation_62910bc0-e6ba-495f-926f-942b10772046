#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析揽收状态的脚本
专门查看aggregationStatus字段，这可能是区分未揽收订单的关键字段
"""

import json
import sys

def analyze_aggregation_status(file_path):
    """分析揽收状态数据"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
    except Exception as e:
        print(f"读取文件失败: {e}")
        return
    
    # 获取基本信息
    total_items = data['result']['totalItemNum']
    page_items = data['result']['pageItems']
    
    print(f"=== 揽收状态分析报告 ===")
    print(f"总订单数量: {total_items}")
    print(f"当前页面订单数量: {len(page_items)}")
    print()
    
    # 统计不同的aggregationStatus值
    aggregation_status_stats = {}
    aggregation_status_v2_stats = {}
    
    # 分析每个订单
    for i, item in enumerate(page_items, 1):
        parent_order_sn = item['parentOrderMap']['parentOrderSn']
        waybill_info_list = item['parentOrderMap']['waybillInfoList']
        
        print(f"--- 订单 {i}: {parent_order_sn} ---")
        
        # 检查每个运单信息
        for waybill in waybill_info_list:
            tracking_number = waybill['trackingNumber']
            ship_company_name = waybill['shipCompanyName']
            aggregation_status = waybill.get('aggregationStatus')
            aggregation_status_v2 = waybill.get('aggregationStatusV2')
            package_delivery_type = waybill.get('packageDeliveryType')
            
            print(f"  运单号: {tracking_number}")
            print(f"  物流公司: {ship_company_name}")
            print(f"  aggregationStatus: {aggregation_status}")
            print(f"  aggregationStatusV2: {aggregation_status_v2}")
            print(f"  packageDeliveryType: {package_delivery_type}")
            
            # 统计aggregationStatus
            if aggregation_status not in aggregation_status_stats:
                aggregation_status_stats[aggregation_status] = []
            aggregation_status_stats[aggregation_status].append(parent_order_sn)
            
            # 统计aggregationStatusV2
            if aggregation_status_v2 not in aggregation_status_v2_stats:
                aggregation_status_v2_stats[aggregation_status_v2] = []
            aggregation_status_v2_stats[aggregation_status_v2].append(parent_order_sn)
            
            # 检查订单包裹信息中的aggregationStatus
            order_list = item.get('orderList', [])
            for order in order_list:
                order_package_info_list = order.get('orderPackageInfoList', [])
                for package_info in order_package_info_list:
                    order_aggregation_status = package_info.get('aggregationStatus')
                    order_aggregation_status_v2 = package_info.get('aggregationStatusV2')
                    print(f"    订单包裹 aggregationStatus: {order_aggregation_status}")
                    print(f"    订单包裹 aggregationStatusV2: {order_aggregation_status_v2}")
        
        print()
    
    # 输出统计信息
    print("=== aggregationStatus 统计 ===")
    for status, orders in aggregation_status_stats.items():
        print(f"状态 {status}: {len(orders)} 个订单")
        if len(orders) <= 5:  # 如果订单数量少，显示具体订单号
            for order in orders:
                print(f"  {order}")
        else:
            print(f"  订单太多，不全部显示...")
    
    print("\n=== aggregationStatusV2 统计 ===")
    for status, orders in aggregation_status_v2_stats.items():
        print(f"状态 {status}: {len(orders)} 个订单")
        if len(orders) <= 5:  # 如果订单数量少，显示具体订单号
            for order in orders:
                print(f"  {order}")
        else:
            print(f"  订单太多，不全部显示...")
    
    print("\n=== 分析结论 ===")
    print("根据您提到的'全是未揽收'的情况，可能的解释：")
    print("1. aggregationStatus = 1 可能表示'未揽收'状态")
    print("2. aggregationStatusV2 = 1 可能也表示'未揽收'状态")
    print("3. 如果所有订单都是相同状态，说明这个页面确实都是未揽收的订单")
    print("4. 物流异常可能就是指这些未揽收的订单")
    
    # 根据您的描述，如果这里面确实有4个物流异常订单，让我们看看是否有其他区分标准
    print("\n=== 寻找可能的4个异常订单 ===")
    print("让我检查是否有其他可能的区分标准...")
    
    # 检查是否有特殊的订单
    special_orders = []
    for i, item in enumerate(page_items, 1):
        parent_order_sn = item['parentOrderMap']['parentOrderSn']
        
        # 检查是否有特殊标记
        has_special_mark = False
        
        # 检查trackAbnormalFlag
        waybill_info_list = item['parentOrderMap']['waybillInfoList']
        for waybill in waybill_info_list:
            track_abnormal_info = waybill.get('trackAbnormalInfo', {})
            if track_abnormal_info.get('trackAbnormalFlag') is not None and track_abnormal_info.get('trackAbnormalFlag') != 0:
                has_special_mark = True
                special_orders.append((parent_order_sn, 'trackAbnormalFlag', track_abnormal_info.get('trackAbnormalFlag')))
        
        # 检查其他可能的特殊字段
        # 可以根据需要添加更多检查条件
        
    if special_orders:
        print(f"找到 {len(special_orders)} 个有特殊标记的订单:")
        for order_sn, field, value in special_orders:
            print(f"  {order_sn}: {field} = {value}")
    else:
        print("未找到明显的特殊标记订单")
        print("可能需要根据其他业务逻辑来区分异常订单")

if __name__ == "__main__":
    file_path = "物流异常.json"
    analyze_aggregation_status(file_path)
