#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新架构使用示例
展示如何使用新的模块化架构
"""

import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from config.config_manager import ConfigManager
from core.token_manager import TokenManager
from api.temu_client import TemuAPIClient, StoreManager
from export.order_exporter import OrderExporter
from utils.logger import setup_logger


def example_1_basic_usage():
    """示例1: 基本使用"""
    print("=" * 60)
    print("示例1: 基本使用")
    print("=" * 60)
    
    # 设置日志
    logger = setup_logger(__name__)
    
    # 初始化配置管理器
    config = ConfigManager()
    logger.info("配置管理器初始化完成")
    
    # 检查Token状态
    is_valid, message = config.validate_token()
    logger.info(f"Token状态: {message}")
    
    # 获取配置信息
    cookies = config.get_cookies()
    logger.info(f"已加载 {len(cookies)} 个cookies")
    
    settings = config.get('settings')
    logger.info(f"设置: {settings}")


def example_2_token_management():
    """示例2: Token管理"""
    print("\n" + "=" * 60)
    print("示例2: Token管理")
    print("=" * 60)
    
    logger = setup_logger(__name__)
    
    # 初始化组件
    config = ConfigManager()
    token_manager = TokenManager(config)
    
    # 执行健康检查
    logger.info("执行Token健康检查...")
    health_result = token_manager.run_health_check()
    
    logger.info(f"检查结果: {health_result['overall_status']}")
    for message in health_result['messages']:
        logger.info(f"  {message}")


def example_3_api_client():
    """示例3: API客户端使用"""
    print("\n" + "=" * 60)
    print("示例3: API客户端使用")
    print("=" * 60)
    
    logger = setup_logger(__name__)
    
    # 初始化组件
    config = ConfigManager()
    token_manager = TokenManager(config)
    api_client = TemuAPIClient(config, token_manager)
    
    # 获取用户信息
    logger.info("获取用户信息...")
    success, result = api_client.get_user_info()
    
    if success:
        logger.info("用户信息获取成功")
        # 提取店铺ID
        success, mall_ids = api_client.get_mall_ids_from_user_info()
        if success:
            logger.info(f"发现店铺ID: {mall_ids}")
        else:
            logger.error(f"提取店铺ID失败: {mall_ids}")
    else:
        logger.error(f"获取用户信息失败: {result}")


def example_4_store_management():
    """示例4: 店铺管理"""
    print("\n" + "=" * 60)
    print("示例4: 店铺管理")
    print("=" * 60)
    
    logger = setup_logger(__name__)
    
    # 初始化组件
    config = ConfigManager()
    token_manager = TokenManager(config)
    store_manager = StoreManager(config, token_manager)
    
    # 获取可用店铺
    logger.info("获取可用店铺...")
    stores = store_manager.get_available_stores()
    
    if stores:
        logger.info(f"发现 {len(stores)} 个可用店铺:")
        for i, store in enumerate(stores, 1):
            logger.info(f"  {i}. {store['mall_name']} (ID: {store['mall_id']})")
    else:
        logger.info("未发现可用店铺")


def example_5_order_export():
    """示例5: 订单导出"""
    print("\n" + "=" * 60)
    print("示例5: 订单导出")
    print("=" * 60)
    
    logger = setup_logger(__name__)
    
    # 初始化组件
    config = ConfigManager()
    token_manager = TokenManager(config)
    api_client = TemuAPIClient(config, token_manager)
    order_exporter = OrderExporter()
    
    # 获取店铺列表
    stores = config.get_mall_ids()
    if not stores:
        logger.warning("未找到店铺列表")
        return
    
    # 获取第一个店铺的订单
    mall_id = stores[0]
    logger.info(f"获取店铺 {mall_id} 的待发货订单...")
    
    success, pending_orders = api_client.get_all_pending_orders(mall_id, max_pages=1)
    
    if success:
        logger.info(f"获取到 {len(pending_orders)} 个待发货订单")
        
        if pending_orders:
            # 提取订单信息
            order_data = order_exporter.extract_order_info(pending_orders)
            
            # 显示统计信息
            order_exporter.show_statistics(order_data)
            
            # 导出到Excel
            filename = f"示例_店铺_{mall_id}_订单.xlsx"
            excel_file = order_exporter.export_to_excel(order_data, filename)
            
            if excel_file:
                logger.info(f"订单已导出到: {excel_file}")
            else:
                logger.error("导出失败")
        else:
            logger.info("没有待发货订单")
    else:
        logger.error(f"获取订单失败: {pending_orders}")


def main():
    """主函数"""
    print("🚀 新架构使用示例")
    print("展示模块化架构的各种功能")
    
    try:
        # 运行各个示例
        example_1_basic_usage()
        example_2_token_management()
        example_3_api_client()
        example_4_store_management()
        example_5_order_export()
        
        print("\n" + "=" * 60)
        print("✅ 所有示例运行完成")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n❌ 示例运行失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
