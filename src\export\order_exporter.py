#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
订单导出器
提供统一的订单数据导出功能
"""

import pandas as pd
import os
import logging
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple
from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
from openpyxl.utils import get_column_letter

logger = logging.getLogger(__name__)


class OrderExporter:
    """订单导出器"""
    
    def __init__(self):
        """初始化订单导出器"""
        # 定义标准的订单列顺序
        self.standard_columns = [
            '订单号', '站点', '订单状态', '子订单号', '应履约件数',
            '商品名称', 'SKUID', 'SKCID', 'SPUID', 'SKU货号', '商品属性',
            '收货人姓名', '收货人联系方式', '备用联系方式', '邮箱',
            '详细地址1', '详细地址2', '详细地址3', '区县', '城市', '省份', '收货地址邮编', '国家',
            '运单号', '物流商', '发货仓',
            '订单创建时间', '要求最晚发货时间', '实际发货时间', '预计送达时间', '实际签收时间'
        ]
    
    def extract_order_info(self, pending_orders: List[Dict]) -> List[Dict[str, Any]]:
        """
        提取订单信息为可导出的格式
        
        Args:
            pending_orders: 待发货订单列表
            
        Returns:
            list: 格式化的订单数据
        """
        logger.info("正在处理订单数据...")
        extracted_data = []
        
        for i, item in enumerate(pending_orders, 1):
            if i % 10 == 0:
                logger.info(f"已处理 {i}/{len(pending_orders)} 个订单")
                
            try:
                parent_order = item['parent_order']
                order = item['order']
                
                # 提取包裹信息
                package_info = order.get('orderPackageInfoList', [{}])[0] if order.get('orderPackageInfoList') else {}
                
                # 提取地址信息
                address_info = {
                    'country': parent_order.get('regionName1', ''),
                    'state': parent_order.get('maskedRegionName2', ''),
                    'city': parent_order.get('maskedRegionName3', ''),
                }
                
                # 计算剩余发货时间
                expect_ship_time = parent_order.get('expectShipLatestTimeStr', '')
                remaining_time = self._calculate_remaining_time(expect_ship_time)
                
                order_data = {
                    # 基本订单信息
                    '订单号': parent_order.get('parentOrderSn', ''),
                    '站点': parent_order.get('siteName', ''),
                    '订单状态': '待发货',
                    '子订单号': order.get('orderSn', ''),
                    '应履约件数': order.get('unShippedQuantity', 0),
                    
                    # 商品信息
                    '商品名称': order.get('goodsName', ''),
                    'SKUID': order.get('skuId', ''),
                    'SKCID': '',  # 需要从其他地方获取
                    'SPUID': order.get('goodsId', ''),
                    'SKU货号': '',  # 需要从其他地方获取
                    '商品属性': order.get('spec', ''),
                    
                    # 收货人信息 - 需要从其他API获取
                    '收货人姓名': '',
                    '收货人联系方式': '',
                    '备用联系方式': '',
                    '邮箱': '',
                    
                    # 地址信息
                    '详细地址1': '',
                    '详细地址2': '',
                    '详细地址3': '',
                    '区县': '',
                    '城市': address_info['city'],
                    '省份': address_info['state'],
                    '收货地址邮编': '',
                    '国家': address_info['country'],
                    
                    # 物流信息
                    '运单号': package_info.get('trackingNumber', ''),
                    '物流商': package_info.get('companyName', ''),
                    '发货仓': package_info.get('sendWarehouseName', ''),
                    
                    # 时间信息
                    '订单创建时间': parent_order.get('parentOrderTimeStr', ''),
                    '要求最晚发货时间': expect_ship_time,
                    '实际发货时间': '',
                    '预计送达时间': parent_order.get('expectDeliveryEndTimeStr', ''),
                    '实际签收时间': '',
                    
                    # 额外信息
                    '剩余发货时间': remaining_time
                }
                
                extracted_data.append(order_data)
                
            except Exception as e:
                logger.error(f"处理订单 {i} 时出错: {e}")
                continue
        
        logger.info(f"订单数据处理完成，共 {len(extracted_data)} 条记录")
        return extracted_data
    
    def _calculate_remaining_time(self, expect_ship_time_str: str) -> str:
        """计算剩余发货时间"""
        if not expect_ship_time_str:
            return "未知"
        
        try:
            expect_time = datetime.strptime(expect_ship_time_str, "%Y-%m-%d %H:%M:%S")
            now = datetime.now()
            remaining = expect_time - now
            
            if remaining.total_seconds() <= 0:
                return "已超时"
            
            days = remaining.days
            hours = remaining.seconds // 3600
            
            if days > 0:
                return f"{days}天{hours}小时"
            else:
                return f"{hours}小时"
        except:
            return "计算失败"
    
    def reorder_columns(self, data: List[Dict]) -> pd.DataFrame:
        """
        重新排列列顺序
        
        Args:
            data: 订单数据列表
            
        Returns:
            DataFrame: 重新排列列顺序后的数据
        """
        try:
            if not data:
                return pd.DataFrame()
            
            df = pd.DataFrame(data)
            
            # 获取现有列
            existing_columns = df.columns.tolist()
            
            # 按标准顺序重新排列列
            ordered_columns = []
            for col in self.standard_columns:
                if col in existing_columns:
                    ordered_columns.append(col)
            
            # 添加不在标准列表中的其他列
            for col in existing_columns:
                if col not in ordered_columns:
                    ordered_columns.append(col)
            
            return df[ordered_columns]
            
        except Exception as e:
            logger.error(f"重新排列列顺序时发生错误: {e}")
            return pd.DataFrame(data) if data else pd.DataFrame()
    
    def export_to_excel(self, data: List[Dict], filename: str, 
                       apply_formatting: bool = True) -> Optional[str]:
        """
        导出数据到Excel文件
        
        Args:
            data: 订单数据
            filename: 文件名
            apply_formatting: 是否应用格式设置
            
        Returns:
            str: 导出的文件路径，失败返回None
        """
        try:
            if not data:
                logger.warning("没有数据可导出")
                return None
            
            # 重新排列列顺序
            df = self.reorder_columns(data)
            
            # 创建目录
            os.makedirs(os.path.dirname(filename), exist_ok=True)
            
            # 创建Excel写入器
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='订单数据', index=False)
                
                # 应用格式设置
                if apply_formatting:
                    self._apply_excel_formatting(writer, '订单数据')
            
            logger.info(f"已导出 {len(data)} 条记录到Excel文件: {filename}")
            return filename
            
        except Exception as e:
            logger.error(f"导出Excel文件时发生错误: {e}")
            return None
    
    def _apply_excel_formatting(self, writer, sheet_name: str):
        """应用Excel格式设置"""
        try:
            worksheet = writer.sheets[sheet_name]
            
            # 定义样式
            header_font = Font(name='Arial', size=10, bold=True)
            data_font = Font(name='Arial', size=9)
            header_fill = PatternFill(start_color='D9D9D9', end_color='D9D9D9', fill_type='solid')
            alignment = Alignment(horizontal='left', vertical='center', wrap_text=True)
            thin_border = Border(
                left=Side(style='thin'), right=Side(style='thin'),
                top=Side(style='thin'), bottom=Side(style='thin')
            )
            
            # 设置标题行格式
            for cell in worksheet[1]:
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = alignment
                cell.border = thin_border
            
            # 设置数据行格式
            for row in worksheet.iter_rows(min_row=2):
                for cell in row:
                    cell.font = data_font
                    cell.alignment = alignment
                    cell.border = thin_border
            
            # 自动调整列宽
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                worksheet.column_dimensions[column_letter].width = adjusted_width
            
            # 冻结首行
            worksheet.freeze_panes = 'A2'
            
        except Exception as e:
            logger.error(f"应用Excel格式时发生错误: {e}")
    
    def show_statistics(self, order_data: List[Dict]):
        """显示订单统计信息"""
        if not order_data:
            return
        
        logger.info(f"\n=== 订单统计信息 ===")
        logger.info(f"总订单数: {len(order_data)}")
        
        # 按站点统计
        site_stats = {}
        for order in order_data:
            site = order.get('站点', '未知')
            site_stats[site] = site_stats.get(site, 0) + 1
        
        logger.info("按站点分布:")
        for site, count in site_stats.items():
            logger.info(f"  {site}: {count} 个订单")
        
        # 紧急订单统计
        urgent_orders = [
            order for order in order_data 
            if '小时' in order.get('剩余发货时间', '') or order.get('剩余发货时间', '') == '已超时'
        ]
        if urgent_orders:
            logger.info(f"\n⚠️  紧急订单: {len(urgent_orders)} 个（需要在24小时内发货或已超时）")
        
        logger.info("=" * 30)
