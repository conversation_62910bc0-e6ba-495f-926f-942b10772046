#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的物流异常判断脚本
只要trackAbnormalFlag不为null，就认为是异常订单
"""

import json

def is_logistics_exception(order_item):
    """
    判断订单是否为物流异常
    参数: order_item - 单个订单的数据
    返回: True表示异常，False表示正常
    """
    waybill_info_list = order_item['parentOrderMap']['waybillInfoList']
    
    for waybill in waybill_info_list:
        track_abnormal_info = waybill.get('trackAbnormalInfo', {})
        track_abnormal_flag = track_abnormal_info.get('trackAbnormalFlag')
        
        # 只要trackAbnormalFlag不为null，就认为是异常
        if track_abnormal_flag is not None:
            return True
    
    return False

def analyze_simple(file_path):
    """简单分析物流异常"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
    except Exception as e:
        print(f"读取文件失败: {e}")
        return
    
    page_items = data['result']['pageItems']
    
    print("=== 物流异常判断结果 ===")
    print(f"总订单数量: {len(page_items)}")
    print()
    
    exception_orders = []
    normal_orders = []
    
    for i, item in enumerate(page_items, 1):
        parent_order_sn = item['parentOrderMap']['parentOrderSn']
        
        if is_logistics_exception(item):
            exception_orders.append(parent_order_sn)
            print(f"订单 {i}: {parent_order_sn} - 【异常】")
        else:
            normal_orders.append(parent_order_sn)
            print(f"订单 {i}: {parent_order_sn} - 正常")
    
    print()
    print("=== 汇总 ===")
    print(f"异常订单数量: {len(exception_orders)}")
    print(f"正常订单数量: {len(normal_orders)}")
    
    if exception_orders:
        print("\n异常订单列表:")
        for order_sn in exception_orders:
            print(f"  {order_sn}")
    
    print("\n=== 判断规则 ===")
    print("字段路径: waybillInfoList[].trackAbnormalInfo.trackAbnormalFlag")
    print("判断逻辑: trackAbnormalFlag != null 即为异常")
    print("说明: 不区分异常严重程度，只要该字段有值就是异常")

if __name__ == "__main__":
    print("=== 测试 物流异常 copy.json ===")
    analyze_simple("物流异常 copy.json")

    print("\n" + "="*50)
    print("=== 测试 物流异常.json ===")
    analyze_simple("物流异常.json")
