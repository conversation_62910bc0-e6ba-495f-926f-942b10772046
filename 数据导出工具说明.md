# 数据导出工具说明

## 概述

本项目提供了一套完整的数据导出工具，专门用于导出Excel表格，格式与`订单导出2025719002.xlsx`保持一致。工具包含以下组件：

1. **通用数据导出工具** (`通用数据导出工具.py`) - 基础的Excel/CSV导出功能
2. **订单数据导出工具** (`订单数据导出工具.py`) - 专门用于订单数据导出，保持标准列排序
3. **多店铺待发货订单导出工具** (`多店铺待发货订单导出工具.py`) - 集成了新导出工具的多店铺订单导出
4. **导出示例** (`导出示例.py`) - 展示各种使用方法的示例代码

## 主要特性

### ✨ 格式一致性
- 与`订单导出2025719002.xlsx`保持完全一致的格式
- 标准的列排序和样式设置
- 自动列宽调整和边框设置

### 📊 多种导出方式
- 单工作表Excel导出
- 多工作表Excel导出
- CSV文件导出
- 自动生成汇总信息

### 🎨 专业格式设置
- 标题行加粗和背景色
- 数据行统一字体和对齐
- 自动冻结首行
- 边框和格式美化

### 🏪 订单数据专用功能
- 标准订单列排序
- 多店铺数据整合
- 汇总统计信息
- 紧急订单提醒

## 安装依赖

```bash
pip install pandas openpyxl
```

## 使用方法

### 1. 通用数据导出

```python
from 通用数据导出工具 import UniversalDataExporter

# 创建导出器
exporter = UniversalDataExporter()

# 准备数据
data = [
    {'姓名': '张三', '年龄': 25, '城市': '北京'},
    {'姓名': '李四', '年龄': 30, '城市': '上海'},
]

# 导出Excel文件
excel_file = exporter.export_to_excel(
    data=data,
    filename="员工信息.xlsx",
    sheet_name="员工数据",
    apply_formatting=True,
    create_summary=True
)

# 导出CSV文件
csv_file = exporter.export_to_csv(data=data, filename="员工信息.csv")
```

### 2. 多工作表导出

```python
# 准备多个数据表
sheets_data = {
    '员工信息': employee_data,
    '部门信息': department_data,
    '项目信息': project_data
}

# 导出多工作表Excel
multi_file = exporter.export_multi_sheet_excel(
    data_dict=sheets_data,
    filename="公司信息汇总.xlsx",
    apply_formatting=True
)
```

### 3. 订单数据导出

```python
from 订单数据导出工具 import OrderDataExporter

# 创建订单导出器
order_exporter = OrderDataExporter()

# 导出单店铺订单
result = order_exporter.export_single_store_orders(
    orders=order_data,
    store_id='123456789',
    store_name='测试店铺',
    output_dir="订单导出"
)

# 导出多店铺订单
multi_store_data = {
    '店铺A': store_a_orders,
    '店铺B': store_b_orders
}

multi_result = order_exporter.export_multi_store_orders(
    store_orders_dict=multi_store_data,
    output_dir="多店铺订单导出",
    create_summary=True
)

# 打印导出结果摘要
order_exporter.print_export_summary(multi_result)
```

## 订单数据标准列排序

订单数据导出工具会自动按照以下标准顺序排列列：

1. 店铺ID
2. 店铺名称
3. 父订单号
4. 子订单号
5. 订单时间
6. 确认时间
7. 最晚发货时间
8. 剩余发货时间
9. 预计送达时间
10. 商品名称
11. 商品规格
12. SKU ID
13. 商品ID
14. 数量
15. 未发货数量
16. 已发货数量
17. 包裹号
18. 快递单号
19. 快递公司
20. 发货仓库
21. 收货国家
22. 收货州/省
23. 收货城市
24. 站点
25. 父订单状态
26. 订单状态
27. 包裹状态
28. 订单包裹状态
29. 仓库名称
30. 是否COD订单
31. 商品缩略图
32. 产品SKU列表
33. 扩展代码

## 输出文件结构

### Excel文件特性
- **主工作表**: 包含所有数据记录
- **汇总信息表**: 包含统计信息和列信息
- **多店铺文件**: 每个店铺单独一个工作表
- **格式设置**: 与`订单导出2025719002.xlsx`完全一致

### 文件命名规则
- 单店铺: `{店铺名称}_订单_{时间戳}.xlsx`
- 多店铺汇总: `汇总_所有店铺订单_{时间戳}.xlsx`
- CSV文件: `{文件名}_{时间戳}.csv`

## 运行示例

```bash
# 运行完整示例
python 导出示例.py

# 运行多店铺导出工具
python 多店铺待发货订单导出工具.py
```

## 文件说明

### 核心文件
- `通用数据导出工具.py` - 基础导出功能类
- `订单数据导出工具.py` - 订单专用导出类
- `多店铺待发货订单导出工具.py` - 多店铺集成工具
- `导出示例.py` - 使用示例和演示

### 依赖文件
- `待发货订单导出工具.py` - 原有的订单获取工具
- `simple_temu_client.py` - Temu API客户端

## 注意事项

1. **Excel格式兼容性**: 使用openpyxl引擎，确保与Excel完全兼容
2. **中文编码**: CSV文件使用utf-8-sig编码，确保中文正确显示
3. **列宽自动调整**: 根据内容自动调整列宽，最大宽度限制为50
4. **工作表名称限制**: Excel工作表名称限制为31个字符
5. **大数据处理**: 对于大量数据，建议分批处理避免内存问题

## 错误处理

工具包含完善的错误处理机制：
- 数据格式验证
- 文件写入异常处理
- 详细的错误信息输出
- 失败时的回退机制

## 扩展功能

可以根据需要扩展以下功能：
- 自定义样式模板
- 数据验证规则
- 图表生成
- 邮件发送集成
- 定时导出任务

## 技术支持

如有问题或建议，请查看代码注释或运行示例文件了解详细用法。
