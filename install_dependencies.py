import subprocess
import sys
import os

def install_package(package):
    """安装Python包"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ 成功安装: {package}")
        return True
    except subprocess.CalledProcessError:
        print(f"❌ 安装失败: {package}")
        return False

def main():
    print("=== 安装运单标签生成器依赖 ===\n")
    
    # 需要安装的包
    packages = [
        "requests",
        "Pillow", 
        "qrcode[pil]"
    ]
    
    # 可选包 (用于PDF生成)
    optional_packages = [
        "reportlab"
    ]
    
    print("正在安装必需的依赖包...")
    success_count = 0
    
    for package in packages:
        print(f"安装 {package}...")
        if install_package(package):
            success_count += 1
    
    print(f"\n必需包安装完成: {success_count}/{len(packages)}")
    
    if success_count == len(packages):
        print("✅ 所有必需依赖安装成功!")
        
        # 尝试安装可选包
        print("\n正在安装可选依赖包 (用于PDF生成)...")
        for package in optional_packages:
            print(f"安装 {package}...")
            install_package(package)
        
        print("\n🎉 安装完成!")
        print("\n使用方法:")
        print("1. 运行简化版本: python simple_label_generator.py")
        print("2. 运行完整版本: python test.py")
        
    else:
        print("❌ 部分依赖安装失败，请手动安装:")
        print("pip install requests Pillow qrcode[pil]")

if __name__ == "__main__":
    main()
