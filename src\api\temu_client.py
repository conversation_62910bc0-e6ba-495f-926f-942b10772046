#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Temu API客户端
提供统一的API接口和错误处理
"""

import requests
import json
import logging
import time
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.support.ui import WebDriverWait

logger = logging.getLogger(__name__)


class TemuAPIClient:
    """Temu API客户端"""
    
    def __init__(self, config_manager, token_manager=None):
        """
        初始化API客户端
        
        Args:
            config_manager: 配置管理器
            token_manager: Token管理器（可选）
        """
        self.config = config_manager
        self.token_manager = token_manager
        self.session = requests.Session()
        self.last_request_time = 0
        self.min_request_interval = 1  # 最小请求间隔（秒）
    
    def _wait_for_rate_limit(self):
        """等待速率限制"""
        current_time = time.time()
        elapsed = current_time - self.last_request_time
        if elapsed < self.min_request_interval:
            time.sleep(self.min_request_interval - elapsed)
        self.last_request_time = time.time()
    
    def _make_request(self, method: str, url: str, **kwargs) -> Tuple[bool, Any]:
        """
        发送HTTP请求
        
        Args:
            method: HTTP方法
            url: 请求URL
            **kwargs: 其他请求参数
            
        Returns:
            tuple: (是否成功, 响应数据或错误信息)
        """
        max_retries = self.config.get_setting('max_retries', 3)
        retry_delay = self.config.get_setting('retry_delay', 2)
        
        for attempt in range(max_retries):
            try:
                # 等待速率限制
                self._wait_for_rate_limit()
                
                # 获取最新的headers和cookies
                headers = self.config.get_headers()
                cookies = self.config.get_cookies()
                
                # 合并请求参数
                request_kwargs = {
                    'headers': headers,
                    'cookies': cookies,
                    'timeout': 30,
                    **kwargs
                }
                
                # 发送请求
                response = self.session.request(method, url, **request_kwargs)
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        return True, data
                    except json.JSONDecodeError:
                        return False, f"响应不是有效的JSON格式"
                else:
                    error_msg = f"HTTP错误: {response.status_code}"
                    logger.warning(f"请求失败 (尝试 {attempt + 1}/{max_retries}): {error_msg}")
                    
                    # 如果是认证错误，尝试刷新Token
                    if response.status_code in [401, 403] and self.token_manager:
                        logger.info("检测到认证错误，尝试刷新Token...")
                        refresh_success, refresh_msg = self.token_manager.auto_refresh_if_needed()
                        if refresh_success:
                            logger.info("Token刷新成功，继续重试...")
                            continue
                    
                    if attempt == max_retries - 1:
                        return False, error_msg
                    
            except requests.exceptions.RequestException as e:
                error_msg = f"请求异常: {e}"
                logger.warning(f"请求失败 (尝试 {attempt + 1}/{max_retries}): {error_msg}")
                
                if attempt == max_retries - 1:
                    return False, error_msg
            
            # 等待后重试
            if attempt < max_retries - 1:
                time.sleep(retry_delay)
        
        return False, "达到最大重试次数"
    
    def get_recent_orders(self, mall_id: str, page_number: int = 1, page_size: int = 500, 
                         query_type: int = 2) -> Tuple[bool, Any]:
        """
        获取最近订单
        
        Args:
            mall_id: 店铺ID
            page_number: 页码
            page_size: 页面大小
            query_type: 查询类型（2=待发货）
            
        Returns:
            tuple: (是否成功, 订单数据或错误信息)
        """
        try:
            url = self.config.get('api_endpoints.orders')
            if not url:
                return False, "缺少订单API端点配置"
            
            # 更新headers中的mallid
            headers = self.config.get_headers()
            headers['mallid'] = str(mall_id)
            
            data = {
                "fulfillmentMode": 0,
                "pageNumber": page_number,
                "pageSize": page_size,
                "queryType": query_type,
                "sortType": 1,
                "timeZone": "UTC+8",
                "parentAfterSalesTag": 0,
                "needBuySignService": 0,
                "sellerNoteLabelList": []
            }
            
            success, result = self._make_request('POST', url, json=data)
            
            if success:
                if result.get('success'):
                    return True, result
                else:
                    error_msg = result.get('errorMsg', '未知API错误')
                    return False, f"API返回错误: {error_msg}"
            else:
                return False, result
                
        except Exception as e:
            error_msg = f"获取订单时出错: {e}"
            logger.error(error_msg)
            return False, error_msg
    
    def get_all_pending_orders(self, mall_id: str, max_pages: int = 10) -> Tuple[bool, List[Dict]]:
        """
        获取所有待发货订单
        
        Args:
            mall_id: 店铺ID
            max_pages: 最大页数
            
        Returns:
            tuple: (是否成功, 订单列表或错误信息)
        """
        try:
            logger.info(f"开始获取店铺 {mall_id} 的待发货订单...")
            all_pending_orders = []
            page_number = 1
            
            while page_number <= max_pages:
                logger.info(f"正在获取第 {page_number} 页...")
                
                success, result = self.get_recent_orders(
                    mall_id=mall_id,
                    page_number=page_number,
                    page_size=500,
                    query_type=2
                )
                
                if not success:
                    return False, result
                
                page_items = result.get('result', {}).get('pageItems', [])
                
                if not page_items:
                    logger.info("没有更多订单数据")
                    break
                
                # 筛选待发货订单
                page_pending_count = 0
                for item in page_items:
                    parent_order = item.get('parentOrderMap', {})
                    order_list = item.get('orderList', [])
                    
                    # 检查是否为待发货状态
                    if (parent_order.get('parentPackageStatus') == 1 and 
                        parent_order.get('parentOrderStatus') == 2):
                        
                        for order in order_list:
                            # 进一步检查订单级别的状态
                            if (order.get('orderPackageStatus') == 1 and 
                                order.get('shippedQuantity', 0) == 0 and 
                                order.get('unShippedQuantity', 0) > 0):
                                
                                all_pending_orders.append({
                                    'parent_order': parent_order,
                                    'order': order
                                })
                                page_pending_count += 1
                
                logger.info(f"第 {page_number} 页找到 {page_pending_count} 个待发货订单")
                
                # 检查是否还有更多页面
                if len(page_items) < 500:
                    logger.info("已获取所有页面")
                    break
                    
                page_number += 1
            
            logger.info(f"总共找到 {len(all_pending_orders)} 个待发货订单")
            return True, all_pending_orders
            
        except Exception as e:
            error_msg = f"获取待发货订单时出错: {e}"
            logger.error(error_msg)
            return False, error_msg
    
    def get_user_info(self) -> Tuple[bool, Any]:
        """
        获取用户信息
        
        Returns:
            tuple: (是否成功, 用户信息或错误信息)
        """
        try:
            url = self.config.get('api_endpoints.user_info')
            if not url:
                return False, "缺少用户信息API端点配置"
            
            success, result = self._make_request('POST', url, json={})
            
            if success:
                if result.get('success'):
                    return True, result
                else:
                    error_msg = result.get('errorMsg', '未知API错误')
                    return False, f"API返回错误: {error_msg}"
            else:
                return False, result
                
        except Exception as e:
            error_msg = f"获取用户信息时出错: {e}"
            logger.error(error_msg)
            return False, error_msg
    
    def get_mall_ids_from_user_info(self) -> Tuple[bool, List[str]]:
        """
        从用户信息中提取店铺ID列表
        
        Returns:
            tuple: (是否成功, 店铺ID列表或错误信息)
        """
        try:
            success, user_info = self.get_user_info()
            if not success:
                return False, user_info
            
            mall_ids = []
            company_list = user_info.get('result', {}).get('companyList', [])
            
            for company in company_list:
                mal_info_list = company.get('malInfoList', [])
                for mall in mal_info_list:
                    mall_id = mall.get('mallId')
                    if mall_id:
                        mall_ids.append(str(mall_id))
            
            logger.info(f"从用户信息中提取到 {len(mall_ids)} 个店铺ID: {mall_ids}")
            return True, mall_ids
            
        except Exception as e:
            error_msg = f"提取店铺ID时出错: {e}"
            logger.error(error_msg)
            return False, error_msg


class StoreManager:
    """店铺管理器"""

    def __init__(self, config_manager, token_manager=None):
        """
        初始化店铺管理器

        Args:
            config_manager: 配置管理器
            token_manager: Token管理器（可选）
        """
        self.config = config_manager
        self.token_manager = token_manager
        self.api_client = TemuAPIClient(config_manager, token_manager)
        self.driver = None
        self.wait = None

    def connect_browser(self) -> bool:
        """连接浏览器"""
        try:
            if self.driver:
                return True

            chrome_options = webdriver.ChromeOptions()
            debug_port = self.config.get_setting('chrome_debug_port', 9337)
            chrome_options.debugger_address = f"127.0.0.1:{debug_port}"

            self.driver = webdriver.Chrome(options=chrome_options)
            self.wait = WebDriverWait(self.driver, 10)

            logger.info("浏览器连接成功")
            return True

        except Exception as e:
            logger.error(f"连接浏览器失败: {e}")
            return False

    def disconnect_browser(self):
        """断开浏览器连接"""
        try:
            if self.driver:
                self.driver.quit()
                self.driver = None
                self.wait = None
                logger.info("浏览器连接已断开")
        except Exception as e:
            logger.error(f"断开浏览器连接失败: {e}")

    def discover_stores(self) -> Tuple[bool, List[Dict[str, Any]]]:
        """
        发现所有可用店铺

        Returns:
            tuple: (是否成功, 店铺列表或错误信息)
        """
        try:
            logger.info("🔍 正在发现所有可用店铺...")

            # 1. 从API获取店铺ID列表
            success, mall_ids = self.api_client.get_mall_ids_from_user_info()
            if not success:
                return False, f"获取店铺ID失败: {mall_ids}"

            if not mall_ids:
                return False, "未找到任何店铺ID"

            # 2. 测试店铺可用性
            available_stores = []

            if not self.connect_browser():
                return False, "无法连接浏览器"

            try:
                for mall_id in mall_ids:
                    store_info = self._test_store_availability(mall_id)
                    if store_info:
                        available_stores.append(store_info)
                        logger.info(f"✅ 店铺 {mall_id} 可用")
                    else:
                        logger.warning(f"❌ 店铺 {mall_id} 不可用")
            finally:
                self.disconnect_browser()

            logger.info(f"✅ 发现 {len(available_stores)} 个可用店铺")

            # 3. 保存店铺列表到配置
            self.config.set_mall_ids([store['mall_id'] for store in available_stores])

            return True, available_stores

        except Exception as e:
            error_msg = f"发现店铺时出错: {e}"
            logger.error(error_msg)
            return False, error_msg

    def _test_store_availability(self, mall_id: str) -> Optional[Dict[str, Any]]:
        """
        测试单个店铺的可用性

        Args:
            mall_id: 店铺ID

        Returns:
            dict: 店铺信息，如果不可用则返回None
        """
        try:
            domains = ["https://agentseller-us.temu.com", "https://agentseller.temu.com"]

            for domain in domains:
                try:
                    test_url = f"{domain}/mmsos/orders.html?init=true&mallId={mall_id}"
                    logger.debug(f"测试店铺: {mall_id} 在 {domain}")

                    self.driver.get(test_url)
                    time.sleep(3)

                    current_url = self.driver.current_url
                    if "login" in current_url.lower() or "error" in current_url.lower():
                        logger.debug(f"店铺 {mall_id} 在 {domain} 需要登录或出错")
                        continue

                    # 获取cookies
                    selenium_cookies = self.driver.get_cookies()
                    cookies_dict = {cookie['name']: cookie['value'] for cookie in selenium_cookies}

                    # 测试API请求
                    success, _ = self.api_client.get_recent_orders(mall_id, page_number=1, page_size=10)
                    if success:
                        return {
                            'mall_id': mall_id,
                            'mall_name': f'店铺_{mall_id}',
                            'domain': domain,
                            'cookies': cookies_dict,
                            'status': 'active'
                        }

                except Exception as e:
                    logger.debug(f"测试店铺 {mall_id} 在 {domain} 失败: {e}")
                    continue

            return None

        except Exception as e:
            logger.error(f"测试店铺 {mall_id} 可用性时出错: {e}")
            return None

    def get_available_stores(self) -> List[Dict[str, Any]]:
        """
        获取可用店铺列表

        Returns:
            list: 店铺列表
        """
        try:
            mall_ids = self.config.get_mall_ids()
            if not mall_ids:
                # 如果没有缓存的店铺列表，尝试发现
                success, stores = self.discover_stores()
                if success:
                    return stores
                else:
                    return []

            # 构造店铺信息
            stores = []
            for mall_id in mall_ids:
                stores.append({
                    'mall_id': mall_id,
                    'mall_name': f'店铺_{mall_id}',
                    'status': 'cached'
                })

            return stores

        except Exception as e:
            logger.error(f"获取可用店铺列表时出错: {e}")
            return []
