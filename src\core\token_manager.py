#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Token自动管理模块
负责Token的自动检测、更新和维护
"""

import time
import logging
from datetime import datetime, timedelta
from selenium import webdriver
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.by import By
from typing import Optional, Dict, Any
import requests

logger = logging.getLogger(__name__)


class TokenManager:
    """Token管理器"""
    
    def __init__(self, config_manager):
        """
        初始化Token管理器
        
        Args:
            config_manager: 配置管理器实例
        """
        self.config = config_manager
        self.driver = None
        self.wait = None
        self.last_check_time = None
        self.consecutive_failures = 0
        self.max_failures = 5
    
    def connect_browser(self) -> bool:
        """
        连接到Chrome浏览器
        
        Returns:
            bool: 连接是否成功
        """
        try:
            if self.driver:
                return True
            
            chrome_options = webdriver.ChromeOptions()
            debug_port = self.config.get_setting('chrome_debug_port', 9337)
            chrome_options.debugger_address = f"127.0.0.1:{debug_port}"
            
            self.driver = webdriver.Chrome(options=chrome_options)
            self.wait = WebDriverWait(self.driver, 10)
            
            logger.info("浏览器连接成功")
            return True
            
        except Exception as e:
            logger.error(f"连接浏览器失败: {e}")
            return False
    
    def disconnect_browser(self):
        """断开浏览器连接"""
        try:
            if self.driver:
                self.driver.quit()
                self.driver = None
                self.wait = None
                logger.info("浏览器连接已断开")
        except Exception as e:
            logger.error(f"断开浏览器连接失败: {e}")
    
    def check_token_validity(self) -> tuple[bool, str]:
        """
        检查Token有效性
        
        Returns:
            tuple: (是否有效, 检查信息)
        """
        try:
            is_valid, message = self.config.validate_token()
            logger.info(f"Token检查结果: {message}")
            
            if not is_valid:
                logger.warning("Token无效或已过期!")
                self.consecutive_failures += 1
            else:
                self.consecutive_failures = 0
            
            return is_valid, message
            
        except Exception as e:
            error_msg = f"检查Token时出错: {e}"
            logger.error(error_msg)
            self.consecutive_failures += 1
            return False, error_msg
    
    def test_api_request(self) -> tuple[bool, str]:
        """
        测试API请求

        Returns:
            tuple: (是否成功, 测试信息)
        """
        try:
            logger.info("测试API请求...")

            # 获取配置
            cookies = self.config.get_cookies()
            headers = self.config.get_headers()
            api_url = self.config.get('api_endpoints.orders')

            if not cookies or not api_url:
                return False, "缺少必要的配置信息"

            # 获取mallid用于测试
            mall_id = cookies.get('mallid') or headers.get('mallid')
            if not mall_id:
                return False, "缺少mallid配置"

            # 更新headers中的mallid
            headers = headers.copy()
            headers['mallid'] = str(mall_id)

            # 构造测试请求
            test_data = {
                "fulfillmentMode": 0,
                "pageNumber": 1,
                "pageSize": 10,
                "queryType": 2,
                "sortType": 1,
                "timeZone": "UTC+8",
                "parentAfterSalesTag": 0,
                "needBuySignService": 0,
                "sellerNoteLabelList": []
            }

            response = requests.post(
                api_url,
                headers=headers,
                cookies=cookies,
                json=test_data,
                timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    order_count = len(result.get('result', {}).get('pageItems', []))
                    success_msg = f"API请求成功，获取到 {order_count} 条订单"
                    logger.info(success_msg)
                    self.consecutive_failures = 0
                    return True, success_msg
                else:
                    error_msg = f"API返回错误: {result.get('errorMsg', '未知错误')}"
                    logger.warning(error_msg)
                    self.consecutive_failures += 1
                    return False, error_msg
            else:
                error_msg = f"API请求失败，状态码: {response.status_code}"
                logger.warning(error_msg)
                self.consecutive_failures += 1
                return False, error_msg

        except Exception as e:
            error_msg = f"API请求测试失败: {e}"
            logger.error(error_msg)
            self.consecutive_failures += 1
            return False, error_msg
    
    def refresh_token_from_browser(self) -> tuple[bool, str]:
        """
        从浏览器刷新Token
        
        Returns:
            tuple: (是否成功, 刷新信息)
        """
        try:
            if not self.connect_browser():
                return False, "无法连接到浏览器"
            
            logger.info("开始从浏览器刷新Token...")
            
            # 访问Temu卖家后台
            self.driver.get("https://agentseller-us.temu.com/mmsos/orders.html")
            time.sleep(3)
            
            # 检查是否需要登录
            current_url = self.driver.current_url
            if "login" in current_url.lower() or "authentication" in current_url.lower():
                return False, "需要重新登录，请手动登录后重试"
            
            # 获取新的cookies
            selenium_cookies = self.driver.get_cookies()
            new_cookies = {cookie['name']: cookie['value'] for cookie in selenium_cookies}
            
            # 验证关键cookies是否存在
            required_cookies = ['seller_temp', 'AccessToken']
            missing_cookies = [cookie for cookie in required_cookies if cookie not in new_cookies]
            
            if missing_cookies:
                return False, f"缺少关键cookies: {missing_cookies}"
            
            # 更新配置
            if self.config.update_cookies(new_cookies):
                # 验证新Token
                is_valid, message = self.config.validate_token()
                if is_valid:
                    success_msg = f"Token刷新成功: {message}"
                    logger.info(success_msg)
                    return True, success_msg
                else:
                    return False, f"刷新的Token无效: {message}"
            else:
                return False, "更新配置失败"
                
        except Exception as e:
            error_msg = f"从浏览器刷新Token失败: {e}"
            logger.error(error_msg)
            return False, error_msg
    
    def auto_refresh_if_needed(self) -> tuple[bool, str]:
        """
        如果需要则自动刷新Token
        
        Returns:
            tuple: (是否成功, 操作信息)
        """
        try:
            # 检查是否启用自动刷新
            if not self.config.get_setting('auto_refresh_token', True):
                return True, "自动刷新已禁用"
            
            # 检查Token是否即将过期
            if not self.config.is_token_expiring_soon(24.0):
                return True, "Token尚未到期，无需刷新"
            
            logger.info("Token即将过期，开始自动刷新...")
            return self.refresh_token_from_browser()
            
        except Exception as e:
            error_msg = f"自动刷新Token失败: {e}"
            logger.error(error_msg)
            return False, error_msg
    
    def run_health_check(self) -> Dict[str, Any]:
        """
        运行健康检查
        
        Returns:
            dict: 检查结果
        """
        logger.info("开始执行Token健康检查...")
        
        check_time = datetime.now()
        results = {
            'check_time': check_time.isoformat(),
            'token_valid': False,
            'api_valid': False,
            'auto_refresh_attempted': False,
            'auto_refresh_success': False,
            'consecutive_failures': self.consecutive_failures,
            'messages': []
        }
        
        try:
            # 1. 检查Token有效性
            token_valid, token_message = self.check_token_validity()
            results['token_valid'] = token_valid
            results['messages'].append(f"Token检查: {token_message}")
            
            # 2. 测试API请求
            api_valid, api_message = self.test_api_request()
            results['api_valid'] = api_valid
            results['messages'].append(f"API测试: {api_message}")
            
            # 3. 如果Token或API有问题，尝试自动刷新
            if not token_valid or not api_valid:
                results['auto_refresh_attempted'] = True
                refresh_success, refresh_message = self.auto_refresh_if_needed()
                results['auto_refresh_success'] = refresh_success
                results['messages'].append(f"自动刷新: {refresh_message}")
                
                # 如果刷新成功，重新测试
                if refresh_success:
                    token_valid, token_message = self.check_token_validity()
                    api_valid, api_message = self.test_api_request()
                    results['token_valid'] = token_valid
                    results['api_valid'] = api_valid
                    results['messages'].append(f"刷新后Token检查: {token_message}")
                    results['messages'].append(f"刷新后API测试: {api_message}")
            
            # 4. 更新检查时间
            self.last_check_time = check_time
            
            # 5. 记录整体状态
            overall_success = results['token_valid'] and results['api_valid']
            status = "正常" if overall_success else "异常"
            results['overall_status'] = status
            results['messages'].append(f"整体状态: {status}")
            
            logger.info(f"健康检查完成 - 状态: {status}")
            
        except Exception as e:
            error_msg = f"健康检查过程中出错: {e}"
            logger.error(error_msg)
            results['messages'].append(error_msg)
            results['overall_status'] = "错误"
        
        return results
    
    def should_alert(self) -> tuple[bool, str]:
        """
        判断是否应该发送警告
        
        Returns:
            tuple: (是否需要警告, 警告信息)
        """
        try:
            # 检查Token过期时间
            if self.config.is_token_expiring_soon(24.0):
                is_valid, message = self.config.validate_token()
                if not is_valid:
                    return True, f"Token已过期: {message}"
                else:
                    return True, f"Token即将过期: {message}"
            
            # 检查连续失败次数
            if self.consecutive_failures >= self.max_failures:
                return True, f"连续 {self.consecutive_failures} 次检查失败"
            
            return False, "状态正常"
            
        except Exception as e:
            return True, f"检查警告状态时出错: {e}"
