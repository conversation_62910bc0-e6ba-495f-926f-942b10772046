import requests
import json


headers = {
    "accept": "application/json, text/plain, */*",
    "accept-language": "zh-CN,zh;q=0.9",
    "content-type": "application/json;charset=UTF-8",
    "mallid": "634418216681369",
    "origin": "https://agentseller-us.temu.com",
    "priority": "u=1, i",
    "referer": "https://agentseller-us.temu.com/mmsos/orders.html?fulfillmentMode=0&queryType=2&sortType=1&timeZone=UTC%2B8&needBuySignService=0&sellerNoteLabelList=&packageAbnormalTypeList=",
    "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": "\"Windows\"",
    "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "same-origin",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36",
    "x-document-referer": "https://agentseller-us.temu.com/mmsos/orders.html",
    "x-phan-data": "0aeJxNzbkNgEAMRFGJpRwCn-txLYjGKG8rgQDZRKOnCf4aO_Ea93ZyuKqIhUng-ORMM71lEVESI82Wv1tSJfkJAJeMEv3Z5OyeT0VeD4cfH3A"
}
cookies = {
    "api_uid": "CiCFY2h2GOC8JgBFCr1vAg==",
    "_nano_fp": "XpmyX5d8X09JnpXYXT_DM45ewfb6MPVoCQxn3W3Q",
    "_bee": "l0HZE50mJJT65bPKyYj4KkmzSkg2Oape",
    "njrpl": "l0HZE50mJJT65bPKyYj4KkmzSkg2Oape",
    "dilx": "DunD0Zy9T02bSF7v66im7",
    "hfsc": "L3yPeow36T7w25XFeg==",
    "timezone": "Asia%2FShanghai",
    "webp": "1",
    "seller_temp": "N_eyJ0IjoiTWJVT2NHWmVkQjNMbU1JcmRHQ1NRTmM0UUh3dW1KUUtrRkoyUGc1OFFrSmJnd053emVzbFVSejBLTFgyTkRDRHFzSXdDbVpsYVhJbzFheHBkbUhRalE9PSIsInYiOjEsInMiOjEwMDAxLCJ1IjoyMzM4NTYxODAwODk3Mn0=",
    "region": "0",
    "mallid": "634418216681369"
}
url = "https://agentseller-us.temu.com/mms/orchid/address/batch/snapshot/order_shipping_address_base_info_query"
data = {
    "parent_order_sn_list": [
        "PO-211-03355330970232673",
        "PO-211-03052432507510895",
        "PO-211-19027145098870876",
        "PO-211-18851155981433719"
    ]
}
data = json.dumps(data, separators=(',', ':'))
response = requests.post(url, headers=headers, cookies=cookies, data=data)

print(response.text)
print(response)