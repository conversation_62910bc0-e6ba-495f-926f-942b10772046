#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多店铺导出器
负责多店铺订单的并发导出和汇总
"""

import os
import logging
import pandas as pd
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Dict, Any, Tuple
from .order_exporter import OrderExporter

logger = logging.getLogger(__name__)


class MultiStoreExporter:
    """多店铺导出器"""
    
    def __init__(self, api_client, output_dir: str = "多店铺待发货订单"):
        """
        初始化多店铺导出器
        
        Args:
            api_client: API客户端
            output_dir: 输出目录
        """
        self.api_client = api_client
        self.output_dir = output_dir
        self.order_exporter = OrderExporter()
    
    def export_single_store(self, store_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        导出单个店铺的待发货订单
        
        Args:
            store_info: 店铺信息
            
        Returns:
            dict: 导出结果
        """
        mall_id = store_info['mall_id']
        mall_name = store_info['mall_name']
        
        logger.info(f"\n🏪 开始处理店铺: {mall_name} (ID: {mall_id})")
        
        try:
            # 获取待发货订单
            success, pending_orders = self.api_client.get_all_pending_orders(mall_id, max_pages=10)
            
            if not success:
                return {
                    'mall_id': mall_id,
                    'mall_name': mall_name,
                    'success': False,
                    'error': pending_orders,
                    'order_count': 0,
                    'files': []
                }
            
            if not pending_orders:
                logger.info(f"   📋 店铺 {mall_name} 没有待发货订单")
                return {
                    'mall_id': mall_id,
                    'mall_name': mall_name,
                    'success': True,
                    'order_count': 0,
                    'files': []
                }
            
            # 提取订单信息
            order_data = self.order_exporter.extract_order_info(pending_orders)
            
            # 为每个订单添加店铺信息
            for order in order_data:
                order['店铺ID'] = mall_id
                order['店铺名称'] = mall_name
            
            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            excel_filename = f"{self.output_dir}/{mall_name}_待发货订单_{timestamp}.xlsx"
            
            # 导出文件
            files = []
            excel_file = self.order_exporter.export_to_excel(order_data, excel_filename)
            if excel_file:
                files.append(excel_file)
            
            logger.info(f"   ✅ 店铺 {mall_name} 完成，导出 {len(order_data)} 个待发货订单")
            
            return {
                'mall_id': mall_id,
                'mall_name': mall_name,
                'success': True,
                'order_count': len(order_data),
                'files': files,
                'orders': order_data
            }
            
        except Exception as e:
            error_msg = f"店铺 {mall_name} 导出失败: {str(e)}"
            logger.error(f"   ❌ {error_msg}")
            return {
                'mall_id': mall_id,
                'mall_name': mall_name,
                'success': False,
                'error': error_msg,
                'order_count': 0,
                'files': []
            }
    
    def export_all_stores(self, stores: List[Dict[str, Any]], 
                         max_workers: int = 3) -> Tuple[List[Dict], List[Dict]]:
        """
        并发导出所有店铺的待发货订单
        
        Args:
            stores: 店铺列表
            max_workers: 最大并发数
            
        Returns:
            tuple: (导出结果列表, 所有订单列表)
        """
        logger.info(f"\n🏭 开始并发处理 {len(stores)} 个店铺的待发货订单...")
        
        # 创建输出目录
        os.makedirs(self.output_dir, exist_ok=True)
        
        all_results = []
        all_orders = []
        
        # 使用线程池并发处理
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_store = {
                executor.submit(self.export_single_store, store): store 
                for store in stores
            }
            
            for future in as_completed(future_to_store):
                store = future_to_store[future]
                try:
                    result = future.result()
                    all_results.append(result)
                    
                    if result['success'] and 'orders' in result:
                        all_orders.extend(result['orders'])
                        
                except Exception as e:
                    logger.error(f"❌ 处理店铺 {store['mall_name']} 时发生异常: {e}")
                    all_results.append({
                        'mall_id': store['mall_id'],
                        'mall_name': store['mall_name'],
                        'success': False,
                        'error': str(e),
                        'order_count': 0,
                        'files': []
                    })
        
        return all_results, all_orders
    
    def generate_summary_files(self, results: List[Dict], all_orders: List[Dict]) -> List[str]:
        """
        生成汇总文件
        
        Args:
            results: 导出结果列表
            all_orders: 所有订单列表
            
        Returns:
            list: 生成的汇总文件列表
        """
        summary_files = []
        
        if not all_orders:
            return summary_files
        
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # 重新排列列顺序，把店铺信息放在前面
            df = pd.DataFrame(all_orders)
            cols = df.columns.tolist()
            if '店铺ID' in cols and '店铺名称' in cols:
                cols.remove('店铺ID')
                cols.remove('店铺名称')
                cols = ['店铺ID', '店铺名称'] + cols
                df = df[cols]
            
            # 汇总Excel文件
            summary_excel = f"{self.output_dir}/汇总_所有店铺待发货订单_{timestamp}.xlsx"
            
            with pd.ExcelWriter(summary_excel, engine='openpyxl') as writer:
                # 所有店铺汇总表
                df.to_excel(writer, sheet_name='所有店铺待发货订单', index=False)
                
                # 按店铺分别创建工作表
                for result in results:
                    if result['success'] and result['order_count'] > 0:
                        store_orders = [
                            order for order in all_orders 
                            if order['店铺ID'] == result['mall_id']
                        ]
                        if store_orders:
                            store_df = pd.DataFrame(store_orders)
                            sheet_name = f"{result['mall_name']}"[:31]  # Excel工作表名称限制
                            store_df.to_excel(writer, sheet_name=sheet_name, index=False)
                
                # 自动调整列宽
                for sheet_name in writer.sheets:
                    worksheet = writer.sheets[sheet_name]
                    for column in worksheet.columns:
                        max_length = 0
                        column_letter = column[0].column_letter
                        for cell in column:
                            try:
                                if len(str(cell.value)) > max_length:
                                    max_length = len(str(cell.value))
                            except:
                                pass
                        adjusted_width = min(max_length + 2, 50)
                        worksheet.column_dimensions[column_letter].width = adjusted_width
            
            summary_files.append(summary_excel)
            logger.info(f"📄 汇总Excel文件已生成: {summary_excel}")
            
        except Exception as e:
            logger.error(f"生成汇总文件时出错: {e}")
        
        return summary_files
    
    def generate_summary_report(self, results: List[Dict], all_orders: List[Dict], 
                              summary_files: List[str]):
        """
        生成汇总报告
        
        Args:
            results: 导出结果列表
            all_orders: 所有订单列表
            summary_files: 汇总文件列表
        """
        logger.info("\n" + "="*80)
        logger.info("🎉 多店铺待发货订单导出完成!")
        logger.info("="*80)
        
        # 统计信息
        total_stores = len(results)
        successful_stores = len([r for r in results if r['success']])
        total_orders = sum(r['order_count'] for r in results if r['success'])
        
        logger.info(f"📊 导出统计:")
        logger.info(f"   处理店铺数: {total_stores}")
        logger.info(f"   成功店铺数: {successful_stores}")
        logger.info(f"   失败店铺数: {total_stores - successful_stores}")
        logger.info(f"   待发货订单总数: {total_orders}")
        
        # 按店铺显示详情
        logger.info(f"\n📋 各店铺详情:")
        for result in results:
            status = "✅" if result['success'] else "❌"
            if result['success']:
                logger.info(f"   {status} {result['mall_name']}: {result['order_count']} 个订单")
            else:
                logger.info(f"   {status} {result['mall_name']}: 失败 - {result.get('error', '未知错误')}")
        
        # 汇总文件信息
        if summary_files:
            logger.info(f"\n📄 汇总文件:")
            for file in summary_files:
                logger.info(f"   📄 {file}")
        
        # 紧急订单统计
        if all_orders:
            urgent_orders = []
            for order in all_orders:
                remaining_time = order.get('剩余发货时间', '')
                if '小时' in remaining_time or remaining_time == '已超时':
                    urgent_orders.append(order)
            
            if urgent_orders:
                logger.info(f"\n⚠️ 紧急订单提醒: {len(urgent_orders)} 个订单需要在24小时内发货或已超时")
                logger.info("   紧急订单店铺分布:")
                urgent_by_store = {}
                for order in urgent_orders:
                    store_name = order.get('店铺名称', '未知')
                    urgent_by_store[store_name] = urgent_by_store.get(store_name, 0) + 1
                
                for store_name, count in urgent_by_store.items():
                    logger.info(f"     {store_name}: {count} 个紧急订单")
        
        logger.info(f"\n📁 输出目录: {os.path.abspath(self.output_dir)}")
        logger.info(f"⏰ 完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info("="*80)
