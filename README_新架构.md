# Temu订单导出工具 - 重构版

## 项目概述

这是Temu多店铺待发货订单导出工具的重构版本，采用模块化架构设计，解决了原版本中的以下问题：

1. **模块依赖混乱** - 重新设计了清晰的模块分层
2. **参数硬编码** - 实现了统一的配置管理
3. **缺乏自动化** - 添加了Token自动检测和更新机制
4. **代码重复** - 提取了通用功能到独立模块

## 新架构特点

### 🏗️ 模块化设计
- **配置管理** (`src/config/`) - 统一管理所有配置信息
- **核心功能** (`src/core/`) - Token管理等核心功能
- **API客户端** (`src/api/`) - 统一的API接口和店铺管理
- **导出工具** (`src/export/`) - 订单数据导出功能
- **工具模块** (`src/utils/`) - 日志等通用工具

### 🔄 自动化管理
- **Token自动检测** - 定期检查Token有效性
- **Token自动刷新** - 检测到过期时自动从浏览器获取新Token
- **店铺自动发现** - 自动获取和测试所有可用店铺
- **健康检查** - 系统状态自动检查和报告

### ⚙️ 配置统一管理
- **动态配置更新** - 支持运行时更新配置
- **参数验证** - 自动验证配置参数的有效性
- **多环境支持** - 支持不同环境的配置文件

## 目录结构

```
├── src/                    # 源代码目录
│   ├── config/            # 配置管理模块
│   │   ├── __init__.py
│   │   └── config_manager.py
│   ├── core/              # 核心功能模块
│   │   ├── __init__.py
│   │   └── token_manager.py
│   ├── api/               # API客户端模块
│   │   ├── __init__.py
│   │   └── temu_client.py
│   ├── export/            # 导出工具模块
│   │   ├── __init__.py
│   │   ├── order_exporter.py
│   │   └── multi_store_exporter.py
│   └── utils/             # 工具模块
│       ├── __init__.py
│       └── logger.py
├── main.py                # 主程序（完整功能）
├── run.py                 # 简化启动脚本
├── monitor_token.py       # Token监控脚本
├── config.json            # 配置文件
└── logs/                  # 日志目录
```

## 使用方法

### 1. 快速开始（推荐）

```bash
# 使用简化启动脚本
python run.py
```

### 2. 完整功能使用

```bash
# 查看帮助
python main.py --help

# 执行健康检查
python main.py --health-check

# 发现并缓存店铺
python main.py --discover-stores

# 导出订单（默认操作）
python main.py --export

# 自动确认，不询问用户
python main.py --auto-confirm

# 强制执行，即使健康检查失败
python main.py --force
```

### 3. Token监控

```bash
# 单次检查Token状态
python monitor_token.py --once

# 持续监控Token（每小时检查一次）
python monitor_token.py

# 自定义检查间隔（每30分钟检查一次）
python monitor_token.py --interval 1800
```

## 配置文件说明

`config.json` 文件包含以下配置项：

```json
{
    "cookies": {
        // 浏览器cookies，会自动更新
    },
    "headers": {
        // HTTP请求头，会自动更新
    },
    "settings": {
        "auto_refresh_token": true,      // 是否自动刷新Token
        "token_check_interval": 3600,    // Token检查间隔（秒）
        "max_retries": 3,                // 最大重试次数
        "retry_delay": 2,                // 重试延迟（秒）
        "chrome_debug_port": 9337        // Chrome调试端口
    },
    "api_endpoints": {
        // API端点配置
    },
    "mall_ids": [
        // 缓存的店铺ID列表
    ]
}
```

## 主要改进

### 1. 解决模块依赖问题
- **之前**: `from 待发货订单导出工具 import TemuOrderExporter`
- **现在**: 清晰的模块导入路径，如 `from api.temu_client import TemuAPIClient`

### 2. 统一配置管理
- **之前**: 配置散布在各个文件中，难以维护
- **现在**: 统一的 `ConfigManager` 管理所有配置，支持动态更新

### 3. 自动Token管理
- **之前**: Token过期后需要手动更新
- **现在**: 自动检测Token状态，过期时自动从浏览器刷新

### 4. 改进的错误处理
- **之前**: 错误处理分散，难以调试
- **现在**: 统一的错误处理和日志记录

### 5. 并发优化
- **之前**: 简单的线程池处理
- **现在**: 更好的并发控制和资源管理

## 兼容性

新版本与原版本在功能上完全兼容，但提供了更好的：
- **稳定性** - 更好的错误处理和恢复机制
- **可维护性** - 清晰的模块结构和代码组织
- **可扩展性** - 易于添加新功能和修改现有功能
- **用户体验** - 更详细的日志和状态反馈

## 迁移指南

如果你正在使用旧版本，可以按以下步骤迁移：

1. **备份现有配置**
   ```bash
   cp config.json config.json.backup
   ```

2. **使用新版本**
   ```bash
   python run.py
   ```

3. **配置会自动迁移**，新版本会读取现有的 `config.json` 并自动适配

## 故障排除

### Token相关问题
```bash
# 检查Token状态
python monitor_token.py --once

# 如果Token有问题，程序会自动尝试刷新
# 如果自动刷新失败，请确保Chrome浏览器已登录Temu卖家后台
```

### 店铺发现问题
```bash
# 重新发现店铺
python main.py --discover-stores
```

### 日志查看
```bash
# 查看最新日志
tail -f logs/temu_exporter_*.log
```

## 技术支持

如果遇到问题，请：
1. 查看日志文件 (`logs/` 目录)
2. 运行健康检查 (`python main.py --health-check`)
3. 检查Token状态 (`python monitor_token.py --once`)

新架构提供了更好的诊断工具，大多数问题都能自动检测和修复。
