#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API调试脚本
直接测试API请求，查看具体问题
"""

import requests
import json
import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from config.config_manager import ConfigManager


def test_user_info_api():
    """测试用户信息API"""
    print("🔍 测试用户信息API...")
    
    config = ConfigManager()
    
    # 获取配置
    cookies = config.get_cookies()
    headers = config.get_headers()
    
    print(f"Cookies数量: {len(cookies)}")
    print(f"Headers数量: {len(headers)}")
    
    # 显示关键信息
    print(f"mallid (cookies): {cookies.get('mallid', 'N/A')}")
    print(f"mallid (headers): {headers.get('mallid', 'N/A')}")
    print(f"AccessToken: {'存在' if cookies.get('AccessToken') else '缺失'}")
    print(f"seller_temp: {'存在' if cookies.get('seller_temp') else '缺失'}")
    
    # 测试用户信息API
    url = "https://seller.kuajingmaihuo.com/bg/quiet/api/mms/userInfo"
    
    print(f"\n📡 发送请求到: {url}")
    
    try:
        response = requests.post(
            url,
            headers=headers,
            cookies=cookies,
            json={},
            timeout=30
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print("✅ 用户信息API成功")
                print(f"响应数据: {json.dumps(data, indent=2, ensure_ascii=False)[:500]}...")
                return True
            except json.JSONDecodeError:
                print("❌ 响应不是有效的JSON")
                print(f"响应内容: {response.text[:500]}")
                return False
        else:
            print(f"❌ 用户信息API失败: {response.status_code}")
            print(f"响应内容: {response.text[:500]}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False


def test_orders_api():
    """测试订单API"""
    print("\n🔍 测试订单API...")
    
    config = ConfigManager()
    
    # 获取配置
    cookies = config.get_cookies()
    headers = config.get_headers()
    
    # 测试订单API
    url = "https://agentseller-us.temu.com/kirogi/bg/mms/recentOrderList"
    
    # 构造测试数据
    test_data = {
        "fulfillmentMode": 0,
        "pageNumber": 1,
        "pageSize": 10,
        "queryType": 2,
        "sortType": 1,
        "timeZone": "UTC+8",
        "parentAfterSalesTag": 0,
        "needBuySignService": 0,
        "sellerNoteLabelList": []
    }
    
    print(f"📡 发送请求到: {url}")
    print(f"请求数据: {json.dumps(test_data, indent=2)}")
    
    try:
        response = requests.post(
            url,
            headers=headers,
            cookies=cookies,
            json=test_data,
            timeout=30
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print("✅ 订单API成功")
                print(f"响应数据: {json.dumps(data, indent=2, ensure_ascii=False)[:500]}...")
                return True
            except json.JSONDecodeError:
                print("❌ 响应不是有效的JSON")
                print(f"响应内容: {response.text[:500]}")
                return False
        else:
            print(f"❌ 订单API失败: {response.status_code}")
            print(f"响应内容: {response.text[:500]}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False


def test_with_old_config():
    """使用旧配置测试"""
    print("\n🔍 使用旧配置测试...")
    
    # 使用旧版本的配置
    MALL_ID = "634418218259845"
    ACCESS_TOKEN = "J73OXMYWK2RLZ5MUWHZGAOF4J6VLXJ6PMAPBNX6VCIJBZBQBT7MA011025f145cf"
    
    COOKIES = {
        "api_uid": "CmwYKWgQb2yulABcDpOgAg==",
        "_bee": "jmWVoOOs6u8MTHaJej00jbAskNLUYanv",
        "njrpl": "jmWVoOOs6u8MTHaJej00jbAskNLUYanv",
        "dilx": "_EsYzO7-cUf1dj6XUNIzM",
        "hfsc": "L3yOfYAw7jv/0pXLfA==",
        "_nano_fp": "XpmYn5m8n5mbXpEJl9_7bNdmhzyXs6pb4Rmyvp~T",
        "AccessToken": ACCESS_TOKEN,
        "user_uin": "BBUSAO4SDG2TDHE743EOIUKYYGS4QSYPO2PKQSWC",
        "isLogin": "1747965800295",
        "timezone": "Asia%2FShanghai",
        "region": "0",
        "webp": "1",
        "seller_temp": "N_eyJ0IjoiSUNPNno1WWhnUS82a1lDOEpGL1laaDZrZDdJNlVURzJ2bVJTTlkyNWpOT3o4VHI4WUNCczdOUHk0b3Q3RklnNElsaVdvOWNrYWVuL2xCZVlQdE5NWUE9PSIsInYiOjEsInMiOjEwMDAxLCJ1IjoyMzkzNDI2MTU1MDQ3OH0=",
        "mallid": str(MALL_ID)
    }
    
    HEADERS = {
        "accept": "application/json, text/plain, */*",
        "accept-language": "zh-CN,zh;q=0.9",
        "content-type": "application/json;charset=UTF-8",
        "mallid": str(MALL_ID),
        "origin": "https://agentseller-us.temu.com",
        "priority": "u=1, i",
        "referer": "https://agentseller-us.temu.com/mmsos/orders.html",
        "sec-ch-ua": "\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"",
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": "\"Windows\"",
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-origin",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36",
        "x-document-referer": "https://agentseller-us.temu.com/mmsos/orders.html",
        "x-phan-data": "0aeJx7xMxiYPiIOSza0NzU0NTI0sjSXAfGMzU1NzFD8MxMLM1iAUBwC6Q"
    }
    
    # 测试订单API
    url = "https://agentseller-us.temu.com/kirogi/bg/mms/recentOrderList"
    
    test_data = {
        "fulfillmentMode": 0,
        "pageNumber": 1,
        "pageSize": 10,
        "queryType": 2,
        "sortType": 1,
        "timeZone": "UTC+8",
        "parentAfterSalesTag": 0,
        "needBuySignService": 0,
        "sellerNoteLabelList": []
    }
    
    print(f"📡 使用旧配置发送请求到: {url}")
    
    try:
        response = requests.post(
            url,
            headers=HEADERS,
            cookies=COOKIES,
            json=test_data,
            timeout=30
        )
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print("✅ 旧配置测试成功")
                print(f"响应数据: {json.dumps(data, indent=2, ensure_ascii=False)[:500]}...")
                return True
            except json.JSONDecodeError:
                print("❌ 响应不是有效的JSON")
                return False
        else:
            print(f"❌ 旧配置测试失败: {response.status_code}")
            print(f"响应内容: {response.text[:500]}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False


def main():
    """主函数"""
    print("🔧 API调试工具")
    print("=" * 50)
    
    # 测试用户信息API
    user_api_success = test_user_info_api()
    
    # 测试订单API
    orders_api_success = test_orders_api()
    
    # 使用旧配置测试
    old_config_success = test_with_old_config()
    
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    print(f"用户信息API: {'✅ 成功' if user_api_success else '❌ 失败'}")
    print(f"订单API: {'✅ 成功' if orders_api_success else '❌ 失败'}")
    print(f"旧配置测试: {'✅ 成功' if old_config_success else '❌ 失败'}")
    
    if old_config_success and not orders_api_success:
        print("\n💡 建议: 旧配置可用，新配置有问题，需要更新配置文件")
    elif not any([user_api_success, orders_api_success, old_config_success]):
        print("\n💡 建议: 所有配置都失效，需要重新获取cookies和token")
    else:
        print("\n💡 建议: 检查具体的错误信息进行针对性修复")


if __name__ == "__main__":
    main()
