#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复403错误的诊断和修复脚本
"""

import sys
import os
import json
import time
from selenium import webdriver
from selenium.webdriver.support.ui import WebDriverWait

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from config.config_manager import ConfigManager
from utils.logger import setup_logger


class Fix403Error:
    """修复403错误的工具类"""
    
    def __init__(self):
        self.logger = setup_logger(__name__)
        self.config = ConfigManager()
        self.driver = None
        self.wait = None
    
    def diagnose_problem(self):
        """诊断问题"""
        self.logger.info("🔍 开始诊断403错误...")
        
        issues = []
        
        # 1. 检查配置一致性
        cookies = self.config.get_cookies()
        headers = self.config.get_headers()
        
        cookies_mallid = cookies.get('mallid', '')
        headers_mallid = headers.get('mallid', '')
        
        if cookies_mallid != headers_mallid:
            issues.append(f"mallid不一致: cookies={cookies_mallid}, headers={headers_mallid}")
        
        # 2. 检查关键cookies
        required_cookies = ['seller_temp', 'AccessToken', 'api_uid']
        missing_cookies = []
        
        for cookie in required_cookies:
            if not cookies.get(cookie):
                missing_cookies.append(cookie)
        
        if missing_cookies:
            issues.append(f"缺少关键cookies: {missing_cookies}")
        
        # 3. 检查headers完整性
        required_headers = ['x-phan-data', 'mallid']
        missing_headers = []
        
        for header in required_headers:
            if not headers.get(header):
                missing_headers.append(header)
        
        if missing_headers:
            issues.append(f"缺少关键headers: {missing_headers}")
        
        # 显示诊断结果
        if issues:
            self.logger.warning("发现以下问题:")
            for i, issue in enumerate(issues, 1):
                self.logger.warning(f"  {i}. {issue}")
        else:
            self.logger.info("配置检查通过")
        
        return issues
    
    def connect_browser(self):
        """连接浏览器"""
        try:
            chrome_options = webdriver.ChromeOptions()
            debug_port = self.config.get_setting('chrome_debug_port', 9337)
            chrome_options.debugger_address = f"127.0.0.1:{debug_port}"
            
            self.driver = webdriver.Chrome(options=chrome_options)
            self.wait = WebDriverWait(self.driver, 10)
            
            self.logger.info("✅ 浏览器连接成功")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 连接浏览器失败: {e}")
            self.logger.info("💡 请确保Chrome浏览器已启动调试模式:")
            self.logger.info("   chrome.exe --remote-debugging-port=9337")
            return False
    
    def refresh_cookies_from_browser(self):
        """从浏览器刷新cookies"""
        try:
            if not self.connect_browser():
                return False
            
            self.logger.info("🔄 从浏览器刷新cookies...")
            
            # 访问Temu卖家后台
            self.driver.get("https://agentseller-us.temu.com/mmsos/orders.html")
            time.sleep(3)
            
            # 检查是否需要登录
            current_url = self.driver.current_url
            if "login" in current_url.lower() or "authentication" in current_url.lower():
                self.logger.error("❌ 需要重新登录，请手动登录后重试")
                return False
            
            # 获取新的cookies
            selenium_cookies = self.driver.get_cookies()
            new_cookies = {cookie['name']: cookie['value'] for cookie in selenium_cookies}
            
            # 获取当前页面的一些动态信息
            try:
                # 尝试获取anti-content
                anti_content = self.driver.execute_script("return window.__ANTI__ || '';")
                if anti_content:
                    self.logger.info(f"获取到anti-content: {anti_content[:50]}...")
            except:
                anti_content = ""
            
            # 验证关键cookies是否存在
            required_cookies = ['seller_temp', 'AccessToken']
            missing_cookies = [cookie for cookie in required_cookies if cookie not in new_cookies]
            
            if missing_cookies:
                self.logger.error(f"❌ 缺少关键cookies: {missing_cookies}")
                return False
            
            # 更新配置
            if self.config.update_cookies(new_cookies):
                # 同时更新headers中的一些动态信息
                headers = self.config.get_headers()
                if 'mallid' in new_cookies:
                    headers['mallid'] = new_cookies['mallid']
                if anti_content:
                    headers['anti-content'] = anti_content
                
                # 更新referer
                headers['referer'] = current_url
                
                # 保存更新后的headers
                self.config.set('headers', headers)
                self.config.save_config()
                
                self.logger.info("✅ cookies和headers更新成功")
                return True
            else:
                self.logger.error("❌ 更新配置失败")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ 刷新cookies失败: {e}")
            return False
        finally:
            if self.driver:
                self.driver.quit()
    
    def test_api_after_fix(self):
        """修复后测试API"""
        try:
            self.logger.info("🧪 测试修复后的API...")
            
            from api.temu_client import TemuAPIClient
            from core.token_manager import TokenManager
            
            token_manager = TokenManager(self.config)
            api_client = TemuAPIClient(self.config, token_manager)
            
            # 测试用户信息API
            success, result = api_client.get_user_info()
            
            if success:
                self.logger.info("✅ 用户信息API测试成功")
                
                # 尝试获取店铺ID
                success, mall_ids = api_client.get_mall_ids_from_user_info()
                if success:
                    self.logger.info(f"✅ 获取到店铺ID: {mall_ids}")
                    return True
                else:
                    self.logger.error(f"❌ 获取店铺ID失败: {mall_ids}")
                    return False
            else:
                self.logger.error(f"❌ 用户信息API测试失败: {result}")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ API测试失败: {e}")
            return False
    
    def fix_403_error(self):
        """修复403错误的主流程"""
        self.logger.info("🔧 开始修复403错误...")
        
        # 1. 诊断问题
        issues = self.diagnose_problem()
        
        # 2. 如果有问题，尝试从浏览器刷新
        if issues:
            self.logger.info("🔄 尝试从浏览器刷新配置...")
            if not self.refresh_cookies_from_browser():
                return False
        
        # 3. 测试修复结果
        if self.test_api_after_fix():
            self.logger.info("🎉 403错误修复成功!")
            return True
        else:
            self.logger.error("❌ 403错误修复失败")
            return False
    
    def show_manual_fix_guide(self):
        """显示手动修复指南"""
        self.logger.info("\n" + "="*60)
        self.logger.info("📖 手动修复指南")
        self.logger.info("="*60)
        
        self.logger.info("如果自动修复失败，请按以下步骤手动修复:")
        self.logger.info("1. 确保Chrome浏览器已启动调试模式:")
        self.logger.info("   chrome.exe --remote-debugging-port=9337")
        self.logger.info("2. 在浏览器中登录Temu卖家后台:")
        self.logger.info("   https://agentseller-us.temu.com/mmsos/orders.html")
        self.logger.info("3. 确保能正常访问订单页面")
        self.logger.info("4. 重新运行修复脚本:")
        self.logger.info("   python fix_403_error.py")
        self.logger.info("5. 或者强制运行主程序:")
        self.logger.info("   python main.py --force")


def main():
    """主函数"""
    print("🔧 Temu订单导出工具 - 403错误修复工具")
    print("=" * 50)
    
    fixer = Fix403Error()
    
    try:
        # 尝试自动修复
        if fixer.fix_403_error():
            print("\n✅ 修复成功! 现在可以正常使用工具了。")
            print("运行以下命令开始导出:")
            print("  python run.py")
        else:
            print("\n❌ 自动修复失败")
            fixer.show_manual_fix_guide()
    
    except Exception as e:
        print(f"\n❌ 修复过程中出错: {e}")
        fixer.show_manual_fix_guide()


if __name__ == "__main__":
    main()
