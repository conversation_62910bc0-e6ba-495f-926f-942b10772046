#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Token监控脚本
定期检查Token状态并自动刷新
"""

import sys
import os
import time
import argparse
from datetime import datetime

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from config.config_manager import ConfigManager
from core.token_manager import TokenManager
from utils.logger import setup_logger, get_default_log_file


class TokenMonitorApp:
    """Token监控应用程序"""
    
    def __init__(self, config_file: str = "config.json"):
        """
        初始化监控应用程序
        
        Args:
            config_file: 配置文件路径
        """
        # 设置日志
        self.logger = setup_logger(__name__, log_file=get_default_log_file())
        
        # 初始化组件
        self.config = ConfigManager(config_file)
        self.token_manager = TokenManager(self.config)
        self.running = False
    
    def run_single_check(self) -> bool:
        """
        执行单次检查
        
        Returns:
            bool: 检查是否成功
        """
        self.logger.info("🔍 执行Token健康检查...")
        
        try:
            # 执行健康检查
            health_result = self.token_manager.run_health_check()
            
            # 显示检查结果
            self.logger.info(f"检查时间: {health_result['check_time']}")
            for message in health_result['messages']:
                self.logger.info(f"  {message}")
            
            overall_success = health_result['overall_status'] == '正常'
            
            if overall_success:
                self.logger.info("✅ Token状态正常")
            else:
                self.logger.warning("⚠️ Token状态异常")
                
                # 检查是否需要警告
                should_alert, alert_message = self.token_manager.should_alert()
                if should_alert:
                    self.logger.warning(f"🚨 警告: {alert_message}")
            
            return overall_success
            
        except Exception as e:
            self.logger.error(f"❌ 检查失败: {e}")
            return False
    
    def run_continuous_monitor(self, check_interval: int = 3600):
        """
        运行持续监控
        
        Args:
            check_interval: 检查间隔（秒）
        """
        self.logger.info(f"🔄 开始Token持续监控，检查间隔: {check_interval} 秒")
        self.running = True
        
        try:
            while self.running:
                # 执行检查
                self.run_single_check()
                
                # 等待下次检查
                self.logger.info(f"⏰ 等待 {check_interval} 秒后进行下次检查...")
                
                # 分段等待，以便响应中断
                wait_time = 0
                while wait_time < check_interval and self.running:
                    time.sleep(min(10, check_interval - wait_time))
                    wait_time += 10
                
        except KeyboardInterrupt:
            self.logger.info("❌ 监控被用户中断")
            self.running = False
        except Exception as e:
            self.logger.error(f"❌ 监控过程中出错: {e}")
            self.running = False
        finally:
            # 清理资源
            try:
                self.token_manager.disconnect_browser()
            except:
                pass
    
    def stop_monitor(self):
        """停止监控"""
        self.running = False
        self.logger.info("🛑 停止监控")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Token监控工具')
    
    parser.add_argument('--config', default='config.json',
                       help='配置文件路径 (默认: config.json)')
    parser.add_argument('--interval', type=int, default=3600,
                       help='检查间隔（秒），默认3600秒（1小时）')
    parser.add_argument('--once', action='store_true',
                       help='只执行一次检查，不进入监控循环')
    
    args = parser.parse_args()
    
    # 创建监控应用程序实例
    monitor = TokenMonitorApp(args.config)
    
    try:
        if args.once:
            # 单次检查
            print("🔍 执行单次Token检查...")
            success = monitor.run_single_check()
            if success:
                print("✅ 检查完成，Token状态正常")
            else:
                print("⚠️ 检查发现问题，请查看日志")
            sys.exit(0 if success else 1)
        else:
            # 持续监控
            print(f"🔄 启动Token持续监控，检查间隔: {args.interval} 秒")
            print("按 Ctrl+C 停止监控")
            monitor.run_continuous_monitor(args.interval)
    
    except KeyboardInterrupt:
        print("\n❌ 用户中断监控")
        monitor.stop_monitor()
    except Exception as e:
        print(f"\n❌ 监控失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
