#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通用数据导出工具
支持导出Excel表格，格式与订单导出2025719002.xlsx保持一致
"""

import pandas as pd
import os
from datetime import datetime
from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
from openpyxl.utils import get_column_letter


class UniversalDataExporter:
    """通用数据导出工具类"""
    
    def __init__(self):
        """初始化导出工具"""
        pass
    
    def export_to_excel(self, data, filename=None, sheet_name='数据导出', 
                       apply_formatting=True, create_summary=True):
        """
        导出数据到Excel文件，格式与订单导出2025719002.xlsx保持一致
        
        Args:
            data: 要导出的数据，可以是DataFrame或字典列表
            filename: 输出文件名，如果为None则自动生成
            sheet_name: 工作表名称
            apply_formatting: 是否应用格式设置
            create_summary: 是否创建汇总信息
            
        Returns:
            str: 导出的文件路径，失败返回None
        """
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"数据导出_{timestamp}.xlsx"
        
        # 检查数据是否为空
        if data is None:
            print("没有数据可导出")
            return None

        if isinstance(data, list) and len(data) == 0:
            print("没有数据可导出")
            return None

        if isinstance(data, pd.DataFrame) and data.empty:
            print("没有数据可导出")
            return None
        
        try:
            # 转换为DataFrame
            if isinstance(data, list):
                df = pd.DataFrame(data)
            elif isinstance(data, pd.DataFrame):
                df = data
            else:
                print("不支持的数据格式")
                return None
            
            # 创建Excel写入器
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                # 写入主数据表
                df.to_excel(writer, sheet_name=sheet_name, index=False)
                
                # 应用格式设置
                if apply_formatting:
                    self._apply_excel_formatting(writer, sheet_name)
                
                # 创建汇总信息表
                if create_summary:
                    self._create_summary_sheet(writer, df, sheet_name)
            
            print(f"已导出 {len(df)} 条记录到Excel文件: {filename}")
            return filename
            
        except Exception as e:
            print(f"导出Excel文件时发生错误: {str(e)}")
            return None
    
    def export_multi_sheet_excel(self, data_dict, filename=None, apply_formatting=True):
        """
        导出多工作表Excel文件
        
        Args:
            data_dict: 字典，键为工作表名称，值为数据
            filename: 输出文件名
            apply_formatting: 是否应用格式设置
            
        Returns:
            str: 导出的文件路径，失败返回None
        """
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"多表数据导出_{timestamp}.xlsx"
        
        if not data_dict:
            print("没有数据可导出")
            return None
        
        try:
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                total_records = 0
                
                for sheet_name, data in data_dict.items():
                    # 检查数据是否为空
                    if data is None:
                        continue
                    if isinstance(data, list) and len(data) == 0:
                        continue
                    if isinstance(data, pd.DataFrame) and data.empty:
                        continue
                    
                    # 转换为DataFrame
                    if isinstance(data, list):
                        df = pd.DataFrame(data)
                    elif isinstance(data, pd.DataFrame):
                        df = data
                    else:
                        continue
                    
                    # 写入工作表
                    df.to_excel(writer, sheet_name=sheet_name[:31], index=False)  # Excel工作表名称限制
                    total_records += len(df)
                    
                    # 应用格式设置
                    if apply_formatting:
                        self._apply_excel_formatting(writer, sheet_name[:31])
            
            print(f"已导出 {total_records} 条记录到多表Excel文件: {filename}")
            return filename
            
        except Exception as e:
            print(f"导出多表Excel文件时发生错误: {str(e)}")
            return None
    
    def _apply_excel_formatting(self, writer, sheet_name):
        """应用Excel格式设置，与订单导出2025719002.xlsx保持一致"""
        try:
            worksheet = writer.sheets[sheet_name]
            
            # 定义样式
            header_font = Font(name='Arial', size=10, bold=True)
            data_font = Font(name='Arial', size=9)
            header_fill = PatternFill(start_color='D9D9D9', end_color='D9D9D9', fill_type='solid')
            alignment = Alignment(horizontal='left', vertical='center', wrap_text=True)
            thin_border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )
            
            # 设置标题行格式
            for cell in worksheet[1]:
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = alignment
                cell.border = thin_border
            
            # 设置数据行格式
            for row in worksheet.iter_rows(min_row=2):
                for cell in row:
                    cell.font = data_font
                    cell.alignment = alignment
                    cell.border = thin_border
            
            # 自动调整列宽，与订单导出2025719002.xlsx保持一致
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                # 设置合适的列宽，与原表格保持一致
                adjusted_width = min(max_length + 2, 50)
                worksheet.column_dimensions[column_letter].width = adjusted_width
            
            # 冻结首行
            worksheet.freeze_panes = 'A2'
            
        except Exception as e:
            print(f"应用Excel格式时发生错误: {str(e)}")
    
    def _create_summary_sheet(self, writer, df, main_sheet_name):
        """创建汇总信息工作表"""
        try:
            summary_data = []
            
            # 基本统计信息
            summary_data.append(['项目', '数值'])
            summary_data.append(['总记录数', len(df)])
            summary_data.append(['导出时间', datetime.now().strftime('%Y-%m-%d %H:%M:%S')])
            summary_data.append(['主工作表', main_sheet_name])
            
            # 列信息统计
            summary_data.append(['', ''])
            summary_data.append(['列名', '非空值数量'])
            for col in df.columns:
                non_null_count = df[col].notna().sum()
                summary_data.append([col, non_null_count])
            
            # 创建汇总DataFrame
            summary_df = pd.DataFrame(summary_data)
            summary_df.to_excel(writer, sheet_name='汇总信息', index=False, header=False)
            
            # 应用格式
            self._apply_summary_formatting(writer.sheets['汇总信息'])
            
        except Exception as e:
            print(f"创建汇总信息时发生错误: {str(e)}")
    
    def _apply_summary_formatting(self, worksheet):
        """为汇总信息表应用格式"""
        try:
            # 定义样式
            header_font = Font(name='Arial', size=10, bold=True)
            data_font = Font(name='Arial', size=9)
            header_fill = PatternFill(start_color='E6F3FF', end_color='E6F3FF', fill_type='solid')
            alignment = Alignment(horizontal='left', vertical='center')
            
            # 设置第一行和第六行为标题行
            for row_num in [1, 6]:
                for cell in worksheet[row_num]:
                    if cell.value:
                        cell.font = header_font
                        cell.fill = header_fill
                        cell.alignment = alignment
            
            # 设置其他行为数据行
            for row in worksheet.iter_rows(min_row=2):
                for cell in row:
                    if cell.row != 6:  # 跳过第6行（已设置为标题行）
                        cell.font = data_font
                        cell.alignment = alignment
            
            # 调整列宽
            worksheet.column_dimensions['A'].width = 20
            worksheet.column_dimensions['B'].width = 15
            
        except Exception as e:
            print(f"应用汇总格式时发生错误: {str(e)}")
    



def main():
    """示例用法"""
    # 创建示例数据
    sample_data = [
        {'姓名': '张三', '年龄': 25, '城市': '北京', '职业': '工程师'},
        {'姓名': '李四', '年龄': 30, '城市': '上海', '职业': '设计师'},
        {'姓名': '王五', '年龄': 28, '城市': '广州', '职业': '产品经理'},
        {'姓名': '赵六', '年龄': 32, '城市': '深圳', '职业': '数据分析师'},
    ]
    
    # 创建导出器
    exporter = UniversalDataExporter()
    
    # 导出Excel文件
    excel_file = exporter.export_to_excel(
        data=sample_data,
        filename="示例数据导出.xlsx",
        sheet_name="员工信息"
    )

    print(f"\n导出完成:")
    print(f"Excel文件: {excel_file}")


if __name__ == "__main__":
    main()
