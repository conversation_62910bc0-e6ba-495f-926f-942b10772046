#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
订单数据导出工具
专门用于导出订单数据，格式与订单导出2025719002.xlsx保持一致
"""

import pandas as pd
import os
from datetime import datetime
from 通用数据导出工具 import UniversalDataExporter


class OrderDataExporter:
    """订单数据导出工具类"""
    
    def __init__(self):
        """初始化订单导出工具"""
        self.exporter = UniversalDataExporter()
        
        # 定义标准的订单列顺序，与模板.xlsx保持一致
        self.standard_columns = [
            '订单号',
            '站点',
            '订单状态',
            '子订单号',
            '应履约件数',
            '商品名称',
            'SKUID',
            'SKCID',
            'SPUID',
            'SKU货号',
            '商品属性',
            '收货人姓名',
            '收货人联系方式',
            '备用联系方式',
            '邮箱',
            '详细地址1',
            '详细地址2',
            '详细地址3',
            '区县',
            '城市',
            '省份',
            '收货地址邮编',
            '国家',
            '运单号',
            '物流商',
            '发货仓',
            '订单创建时间',
            '要求最晚发货时间',
            '实际发货时间',
            '预计送达时间',
            '实际签收时间'
        ]
    
    def reorder_columns(self, data):
        """
        重新排列列顺序，与订单导出2025719002.xlsx保持一致

        Args:
            data: 订单数据列表或DataFrame

        Returns:
            DataFrame: 重新排列列顺序后的数据
        """
        try:
            if isinstance(data, list):
                if len(data) == 0:
                    return pd.DataFrame()
                df = pd.DataFrame(data)
            elif isinstance(data, pd.DataFrame):
                if data.empty:
                    return pd.DataFrame()
                df = data.copy()
            else:
                raise ValueError("不支持的数据格式")

            # 获取现有列
            existing_columns = df.columns.tolist()

            # 按标准顺序重新排列列
            ordered_columns = []
            for col in self.standard_columns:
                if col in existing_columns:
                    ordered_columns.append(col)

            # 添加不在标准列表中的其他列
            for col in existing_columns:
                if col not in ordered_columns:
                    ordered_columns.append(col)

            return df[ordered_columns]
        except Exception as e:
            print(f"重新排列列顺序时发生错误: {str(e)}")
            # 如果出错，返回原始DataFrame
            if isinstance(data, pd.DataFrame):
                return data
            else:
                return pd.DataFrame(data) if data else pd.DataFrame()
    
    def export_single_store_orders(self, orders, store_id=None, store_name=None, 
                                 output_dir="订单导出", filename=None):
        """
        导出单个店铺的订单数据
        
        Args:
            orders: 订单数据列表
            store_id: 店铺ID
            store_name: 店铺名称
            output_dir: 输出目录
            filename: 文件名，如果为None则自动生成
            
        Returns:
            dict: 导出结果
        """
        # 检查订单数据是否为空
        if orders is None:
            print("没有订单数据可导出")
            return {'success': False, 'message': '没有订单数据'}

        if isinstance(orders, pd.DataFrame) and orders.empty:
            print("没有订单数据可导出")
            return {'success': False, 'message': '没有订单数据'}

        if isinstance(orders, list) and len(orders) == 0:
            print("没有订单数据可导出")
            return {'success': False, 'message': '没有订单数据'}
        
        try:
            # 创建输出目录
            os.makedirs(output_dir, exist_ok=True)
            
            # 重新排列列顺序
            df = self.reorder_columns(orders)
            
            # 生成文件名
            if not filename:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                if store_name:
                    filename = f"{output_dir}/{store_name}_订单_{timestamp}.xlsx"
                elif store_id:
                    filename = f"{output_dir}/店铺_{store_id}_订单_{timestamp}.xlsx"
                else:
                    filename = f"{output_dir}/订单导出_{timestamp}.xlsx"
            else:
                filename = f"{output_dir}/{filename}"
            
            # 导出Excel文件
            excel_file = self.exporter.export_to_excel(
                data=df,
                filename=filename,
                sheet_name='订单数据',
                apply_formatting=True,
                create_summary=True
            )
            
            if excel_file:
                return {
                    'success': True,
                    'filename': excel_file,
                    'order_count': len(df),
                    'store_id': store_id,
                    'store_name': store_name
                }
            else:
                return {'success': False, 'message': '导出失败'}
                
        except Exception as e:
            error_msg = f"导出单店铺订单时发生错误: {str(e)}"
            print(error_msg)
            return {'success': False, 'message': error_msg}
    
    def export_multi_store_orders(self, store_orders_dict, output_dir="多店铺订单导出", 
                                create_summary=True):
        """
        导出多店铺订单数据
        
        Args:
            store_orders_dict: 字典，键为店铺信息，值为订单数据
            output_dir: 输出目录
            create_summary: 是否创建汇总文件
            
        Returns:
            dict: 导出结果
        """
        if not store_orders_dict or len(store_orders_dict) == 0:
            print("没有店铺订单数据可导出")
            return {'success': False, 'message': '没有店铺订单数据'}
        
        try:
            # 创建输出目录
            os.makedirs(output_dir, exist_ok=True)
            
            results = []
            all_orders = []
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # 导出各店铺订单
            for store_info, orders in store_orders_dict.items():
                # 检查订单数据是否为空
                if orders is None:
                    continue
                if isinstance(orders, pd.DataFrame) and orders.empty:
                    continue
                if isinstance(orders, list) and len(orders) == 0:
                    continue
                
                # 解析店铺信息
                if isinstance(store_info, dict):
                    store_id = store_info.get('store_id', '')
                    store_name = store_info.get('store_name', f'店铺_{store_id}')
                else:
                    store_id = str(store_info)
                    store_name = f'店铺_{store_id}'
                
                # 重新排列列顺序
                df = self.reorder_columns(orders)
                
                # 导出单店铺文件
                store_filename = f"{store_name}_订单_{timestamp}.xlsx"
                result = self.export_single_store_orders(
                    orders=df.to_dict('records'),
                    store_id=store_id,
                    store_name=store_name,
                    output_dir=output_dir,
                    filename=store_filename
                )
                
                results.append(result)
                
                # 收集所有订单用于汇总
                if result['success']:
                    all_orders.extend(df.to_dict('records'))
            
            # 创建汇总文件
            summary_files = []
            if create_summary and all_orders:
                # 重新排列汇总数据的列顺序
                summary_df = self.reorder_columns(all_orders)
                
                # 汇总Excel文件
                summary_excel = f"{output_dir}/汇总_所有店铺订单_{timestamp}.xlsx"
                
                # 创建多工作表Excel文件
                sheets_data = {'所有店铺订单': summary_df}
                
                # 为每个店铺创建单独的工作表
                for store_info, orders in store_orders_dict.items():
                    if not orders:
                        continue
                    
                    if isinstance(store_info, dict):
                        store_name = store_info.get('store_name', f'店铺_{store_info.get("store_id", "")}')
                    else:
                        store_name = f'店铺_{store_info}'
                    
                    store_df = self.reorder_columns(orders)
                    sheets_data[store_name[:31]] = store_df  # Excel工作表名称限制
                
                summary_file = self.exporter.export_multi_sheet_excel(
                    data_dict=sheets_data,
                    filename=summary_excel,
                    apply_formatting=True
                )
                
                if summary_file:
                    summary_files.append(summary_file)
                
                # 汇总CSV文件
                summary_csv = f"{output_dir}/汇总_所有店铺订单_{timestamp}.csv"
                csv_file = self.exporter.export_to_csv(
                    data=summary_df,
                    filename=summary_csv
                )
                
                if csv_file:
                    summary_files.append(csv_file)
            
            # 统计结果
            successful_stores = len([r for r in results if r['success']])
            total_orders = sum(r['order_count'] for r in results if r['success'])
            
            return {
                'success': True,
                'total_stores': len(store_orders_dict),
                'successful_stores': successful_stores,
                'total_orders': total_orders,
                'store_results': results,
                'summary_files': summary_files,
                'output_dir': output_dir
            }
            
        except Exception as e:
            error_msg = f"导出多店铺订单时发生错误: {str(e)}"
            print(error_msg)
            return {'success': False, 'message': error_msg}
    
    def print_export_summary(self, result):
        """打印导出结果摘要"""
        if not result['success']:
            print(f"❌ 导出失败: {result['message']}")
            return
        
        print("\n" + "="*80)
        print("🎉 订单数据导出完成!")
        print("="*80)
        
        if 'total_stores' in result:
            # 多店铺导出结果
            print(f"📊 导出统计:")
            print(f"   处理店铺数: {result['total_stores']}")
            print(f"   成功店铺数: {result['successful_stores']}")
            print(f"   失败店铺数: {result['total_stores'] - result['successful_stores']}")
            print(f"   订单总数: {result['total_orders']}")
            
            print(f"\n📋 各店铺详情:")
            for store_result in result['store_results']:
                status = "✅" if store_result['success'] else "❌"
                if store_result['success']:
                    print(f"   {status} {store_result['store_name']}: {store_result['order_count']} 个订单")
                else:
                    print(f"   {status} {store_result.get('store_name', '未知店铺')}: 失败")
            
            if result['summary_files']:
                print(f"\n📄 汇总文件:")
                for file in result['summary_files']:
                    print(f"   📄 {file}")
        else:
            # 单店铺导出结果
            print(f"📊 导出统计:")
            print(f"   店铺: {result.get('store_name', '未知')}")
            print(f"   订单数: {result['order_count']}")
            print(f"   文件: {result['filename']}")
        
        print(f"\n📁 输出目录: {os.path.abspath(result.get('output_dir', '.'))}")
        print(f"⏰ 完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("="*80)


def main():
    """示例用法"""
    # 创建示例订单数据
    sample_orders = [
        {
            '订单号': 'PO-001',
            '站点': '美国站',
            '订单状态': '待发货',
            '子订单号': 'SO-001',
            '应履约件数': 2,
            '商品名称': '测试商品A',
            'SKUID': 'SKU001',
            'SKCID': 'SKC001',
            'SPUID': 'SPU001',
            'SKU货号': 'GOODS001',
            '商品属性': '红色/L码',
            '收货人姓名': '张三',
            '收货人联系方式': '13800138000',
            '备用联系方式': '',
            '邮箱': '<EMAIL>',
            '详细地址1': '123 Main St',
            '详细地址2': 'Apt 4B',
            '详细地址3': '',
            '区县': 'Manhattan',
            '城市': '纽约',
            '省份': 'NY',
            '收货地址邮编': '10001',
            '国家': '美国',
            '运单号': '',
            '物流商': '',
            '发货仓': '',
            '订单创建时间': '2025-01-19 10:00:00',
            '要求最晚发货时间': '2025-01-21 18:00:00',
            '实际发货时间': '',
            '预计送达时间': '2025-01-25 18:00:00',
            '实际签收时间': ''
        },
        {
            '订单号': 'PO-002',
            '站点': '北美站',
            '订单状态': '待发货',
            '子订单号': 'SO-002',
            '应履约件数': 1,
            '商品名称': '测试商品B',
            'SKUID': 'SKU002',
            'SKCID': 'SKC002',
            'SPUID': 'SPU002',
            'SKU货号': 'GOODS002',
            '商品属性': '蓝色/M码',
            '收货人姓名': '李四',
            '收货人联系方式': '13900139000',
            '备用联系方式': '',
            '邮箱': '<EMAIL>',
            '详细地址1': '456 Oak Ave',
            '详细地址2': 'Unit 2A',
            '详细地址3': '',
            '区县': 'Toronto',
            '城市': '多伦多',
            '省份': 'ON',
            '收货地址邮编': 'M5V 3A8',
            '国家': '加拿大',
            '运单号': '',
            '物流商': '',
            '发货仓': '',
            '订单创建时间': '2025-01-19 11:00:00',
            '要求最晚发货时间': '2025-01-21 19:00:00',
            '实际发货时间': '',
            '预计送达时间': '2025-01-25 19:00:00',
            '实际签收时间': ''
        }
    ]
    
    # 创建导出器
    exporter = OrderDataExporter()
    
    # 导出单店铺订单
    print("🔄 导出单店铺订单...")
    result = exporter.export_single_store_orders(
        orders=sample_orders,
        store_id='123456789',
        store_name='测试店铺'
    )
    exporter.print_export_summary(result)


if __name__ == "__main__":
    main()
