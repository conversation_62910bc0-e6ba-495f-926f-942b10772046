#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
订单导出测试脚本
"""

import pandas as pd
from 订单数据导出工具 import OrderDataExporter

def test_order_export():
    """测试订单导出功能"""
    print("🔄 测试订单导出功能...")
    
    # 创建简单的订单数据
    orders = [
        {
            '店铺ID': '123456789',
            '店铺名称': '测试店铺',
            '父订单号': 'PO-001',
            '子订单号': 'SO-001',
            '订单时间': '2025-01-19 10:00:00',
            '商品名称': '测试商品A',
            '数量': 2,
            '收货国家': '美国',
            '站点': '美国站'
        }
    ]
    
    print(f"订单数据: {orders}")
    print(f"订单数据类型: {type(orders)}")
    print(f"订单数据长度: {len(orders)}")
    
    # 创建导出器
    exporter = OrderDataExporter()
    
    try:
        # 测试列重排
        print("\n🔄 测试列重排...")
        df = exporter.reorder_columns(orders)
        print(f"重排后DataFrame形状: {df.shape}")
        print(f"重排后列: {df.columns.tolist()}")
        
        # 测试单店铺导出
        print("\n🔄 测试单店铺导出...")
        result = exporter.export_single_store_orders(
            orders=orders,
            store_id='123456789',
            store_name='测试店铺',
            output_dir="测试订单导出"
        )
        
        print(f"导出结果: {result}")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_order_export()
