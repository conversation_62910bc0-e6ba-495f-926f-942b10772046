#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理模块
统一管理所有配置信息，支持动态更新和验证
"""

import json
import os
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, Optional
import base64
import re

logger = logging.getLogger(__name__)


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: str = "config.json"):
        """
        初始化配置管理器
        
        Args:
            config_file: 配置文件路径
        """
        self.config_file = config_file
        self.config = {}
        self.load_config()
    
    def load_config(self) -> bool:
        """
        加载配置文件
        
        Returns:
            bool: 加载是否成功
        """
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.config = json.load(f)
                logger.info(f"配置文件加载成功: {self.config_file}")
                return True
            else:
                logger.warning(f"配置文件不存在: {self.config_file}")
                self._create_default_config()
                return False
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            return False
    
    def save_config(self) -> bool:
        """
        保存配置文件
        
        Returns:
            bool: 保存是否成功
        """
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=4, ensure_ascii=False)
            logger.info(f"配置文件保存成功: {self.config_file}")
            return True
        except Exception as e:
            logger.error(f"保存配置文件失败: {e}")
            return False
    
    def _create_default_config(self):
        """创建默认配置"""
        self.config = {
            "cookies": {},
            "headers": {
                "accept": "application/json, text/plain, */*",
                "accept-language": "zh-CN,zh;q=0.9",
                "content-type": "application/json;charset=UTF-8",
                "origin": "https://agentseller-us.temu.com",
                "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
            },
            "settings": {
                "auto_refresh_token": True,
                "token_check_interval": 3600,
                "max_retries": 3,
                "retry_delay": 2,
                "chrome_debug_port": 9337
            },
            "api_endpoints": {
                "orders": "https://agentseller-us.temu.com/kirogi/bg/mms/recentOrderList",
                "user_info": "https://seller.kuajingmaihuo.com/bg/quiet/api/mms/userInfo"
            }
        }
        self.save_config()
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            key: 配置键，支持点分隔的嵌套键如 'cookies.api_uid'
            default: 默认值
            
        Returns:
            配置值
        """
        keys = key.split('.')
        value = self.config
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any) -> bool:
        """
        设置配置值
        
        Args:
            key: 配置键，支持点分隔的嵌套键
            value: 配置值
            
        Returns:
            bool: 设置是否成功
        """
        keys = key.split('.')
        config = self.config
        
        try:
            # 创建嵌套结构
            for k in keys[:-1]:
                if k not in config:
                    config[k] = {}
                config = config[k]
            
            # 设置最终值
            config[keys[-1]] = value
            return True
        except Exception as e:
            logger.error(f"设置配置值失败: {key} = {value}, 错误: {e}")
            return False
    
    def update_cookies(self, cookies: Dict[str, str]) -> bool:
        """
        更新cookies配置
        
        Args:
            cookies: 新的cookies字典
            
        Returns:
            bool: 更新是否成功
        """
        try:
            if not isinstance(cookies, dict):
                logger.error("cookies必须是字典类型")
                return False
            
            # 更新cookies
            self.config.setdefault('cookies', {}).update(cookies)
            
            # 更新headers中的mallid
            if 'mallid' in cookies:
                self.config.setdefault('headers', {})['mallid'] = cookies['mallid']
            
            # 记录更新时间
            self.config['last_cookie_update'] = datetime.now().isoformat()
            
            return self.save_config()
        except Exception as e:
            logger.error(f"更新cookies失败: {e}")
            return False
    
    def get_cookies(self) -> Dict[str, str]:
        """获取cookies配置"""
        return self.config.get('cookies', {})
    
    def get_headers(self) -> Dict[str, str]:
        """获取headers配置"""
        return self.config.get('headers', {})
    
    def validate_token(self) -> tuple[bool, str]:
        """
        验证token有效性
        
        Returns:
            tuple: (是否有效, 验证信息)
        """
        try:
            seller_temp = self.get('cookies.seller_temp', '')
            if not seller_temp:
                return False, "seller_temp为空"
            
            # 解码seller_temp
            is_valid, message = self._decode_seller_temp(seller_temp)
            return is_valid, message
            
        except Exception as e:
            return False, f"验证token时出错: {e}"
    
    def _decode_seller_temp(self, seller_temp: str) -> tuple[bool, str]:
        """
        解码seller_temp获取过期时间
        
        Args:
            seller_temp: seller_temp值
            
        Returns:
            tuple: (是否有效, 解码信息)
        """
        try:
            if not seller_temp or not seller_temp.startswith('N_'):
                return False, "seller_temp格式无效"
            
            # 去掉前缀N_
            encoded_data = seller_temp[2:]
            
            # Base64解码
            decoded_bytes = base64.b64decode(encoded_data)
            decoded_str = decoded_bytes.decode('utf-8')
            
            # 解析JSON
            data = json.loads(decoded_str)
            
            # 获取过期时间戳（毫秒）
            expire_timestamp = data.get('u', 0)
            if expire_timestamp == 0:
                return False, "无法获取过期时间"
            
            # 转换为秒并创建datetime对象
            expire_time = datetime.fromtimestamp(expire_timestamp / 1000)
            current_time = datetime.now()
            
            if expire_time <= current_time:
                return False, f"Token已过期，过期时间: {expire_time.strftime('%Y-%m-%d %H:%M:%S')}"
            
            # 计算剩余时间
            remaining = expire_time - current_time
            remaining_hours = remaining.total_seconds() / 3600
            
            return True, f"Token有效，剩余 {remaining_hours:.1f} 小时，过期时间: {expire_time.strftime('%Y-%m-%d %H:%M:%S')}"
            
        except Exception as e:
            return False, f"解码seller_temp失败: {e}"
    
    def is_token_expiring_soon(self, hours_threshold: float = 24.0) -> bool:
        """
        检查token是否即将过期
        
        Args:
            hours_threshold: 小时阈值
            
        Returns:
            bool: 是否即将过期
        """
        try:
            seller_temp = self.get('cookies.seller_temp', '')
            if not seller_temp:
                return True
            
            is_valid, message = self._decode_seller_temp(seller_temp)
            if not is_valid:
                return True
            
            # 从消息中提取剩余小时数
            hours_match = re.search(r'剩余 ([\d.]+) 小时', message)
            if hours_match:
                remaining_hours = float(hours_match.group(1))
                return remaining_hours < hours_threshold
            
            return True
        except Exception:
            return True
    
    def get_mall_ids(self) -> list:
        """获取所有店铺ID列表"""
        return self.config.get('mall_ids', [])
    
    def set_mall_ids(self, mall_ids: list) -> bool:
        """设置店铺ID列表"""
        try:
            self.config['mall_ids'] = mall_ids
            return self.save_config()
        except Exception as e:
            logger.error(f"设置店铺ID列表失败: {e}")
            return False
    
    def get_setting(self, key: str, default: Any = None) -> Any:
        """获取设置项"""
        return self.config.get('settings', {}).get(key, default)
    
    def set_setting(self, key: str, value: Any) -> bool:
        """设置设置项"""
        try:
            self.config.setdefault('settings', {})[key] = value
            return self.save_config()
        except Exception as e:
            logger.error(f"设置设置项失败: {key} = {value}, 错误: {e}")
            return False
