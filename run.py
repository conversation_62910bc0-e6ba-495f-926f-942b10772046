#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化启动脚本
提供简单的交互式界面
"""

import os
import sys
from main import TemuOrderExportApp


def main():
    """简化的主函数"""
    print("🚀 多店铺Temu待发货订单导出工具")
    print("=" * 50)
    
    # 创建应用程序实例
    app = TemuOrderExportApp()
    
    try:
        # 1. 健康检查
        print("\n1️⃣ 执行系统健康检查...")
        if not app.run_health_check():
            print("⚠️ 健康检查发现问题，但继续执行...")
        
        # 2. 发现店铺
        print("\n2️⃣ 发现可用店铺...")
        stores = app.store_manager.get_available_stores()
        
        if not stores:
            print("🔍 未找到缓存的店铺，开始发现...")
            if not app.discover_stores():
                print("❌ 发现店铺失败")
                return
            stores = app.store_manager.get_available_stores()
        
        if not stores:
            print("❌ 未发现任何可用店铺")
            return
        
        print(f"✅ 发现 {len(stores)} 个可用店铺")
        
        # 3. 导出订单
        print("\n3️⃣ 开始导出订单...")
        success = app.export_orders(auto_confirm=True)
        
        if success:
            print("\n🎉 导出完成!")
        else:
            print("\n❌ 导出失败")
    
    except KeyboardInterrupt:
        print("\n❌ 用户中断操作")
    except Exception as e:
        print(f"\n❌ 运行时出错: {e}")
    finally:
        # 清理资源
        try:
            app.token_manager.disconnect_browser()
            app.store_manager.disconnect_browser()
        except:
            pass
        
        input("\n按回车键退出...")


if __name__ == "__main__":
    main()
